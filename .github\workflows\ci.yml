name: CI

on: [push, pull_request]

jobs:

  test-ubuntu-latest:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v1
    - name: make
      run: make
    - name: test
      run: |
        sudo apt-get install tcl8.5
        ./runtest --verbose
    - name: module api test
      run: ./runtest-moduleapi --verbose

  build-ubuntu-old:
    runs-on: ubuntu-16.04
    steps:
    - uses: actions/checkout@v1
    - name: make
      run: make

  build-macos-latest:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v1
    - name: make
      run: make

  build-32bit:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v1
    - name: make
      run: |
        sudo apt-get update && sudo apt-get install libc6-dev-i386
        make 32bit

  build-libc-malloc:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v1
    - name: make
      run: make MALLOC=libc

