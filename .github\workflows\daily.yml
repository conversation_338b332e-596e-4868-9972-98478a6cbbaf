name: Daily

on:
  pull_request:
    branches:
      # any PR to a release branch.
      - '[0-9].[0-9]'
  schedule:
    - cron: '0 0 * * *'

jobs:

  test-jemalloc:
    runs-on: ubuntu-latest
    timeout-minutes: 14400
    steps:
    - uses: actions/checkout@v1
    - name: make
      run: make
    - name: test
      run: |
        sudo apt-get install tcl8.5
        ./runtest --accurate --verbose
    - name: module api test
      run: ./runtest-moduleapi --verbose
    - name: sentinel tests
      run: ./runtest-sentinel
    - name: cluster tests
      run: ./runtest-cluster

  test-libc-malloc:
    runs-on: ubuntu-latest
    timeout-minutes: 14400
    steps:
    - uses: actions/checkout@v1
    - name: make
      run: make MALLOC=libc
    - name: test
      run: |
        sudo apt-get install tcl8.5
        ./runtest --accurate --verbose
    - name: module api test
      run: ./runtest-moduleapi --verbose
    - name: sentinel tests
      run: ./runtest-sentinel
    - name: cluster tests
      run: ./runtest-cluster

  test-32bit:
    runs-on: ubuntu-latest
    timeout-minutes: 14400
    steps:
    - uses: actions/checkout@v1
    - name: make
      run: |
        sudo apt-get update && sudo apt-get install libc6-dev-i386
        make 32bit
    - name: test
      run: |
        sudo apt-get install tcl8.5
        ./runtest --accurate --verbose
    - name: module api test
      run: |
        make -C tests/modules 32bit # the script below doesn't have an argument, we must build manually ahead of time
        ./runtest-moduleapi --verbose
    - name: sentinel tests
      run: ./runtest-sentinel
    - name: cluster tests
      run: ./runtest-cluster

  test-tls:
    runs-on: ubuntu-latest
    timeout-minutes: 14400
    steps:
    - uses: actions/checkout@v1
    - name: make
      run: |
        make BUILD_TLS=yes
    - name: test
      run: |
        sudo apt-get install tcl8.5 tcl-tls
        ./utils/gen-test-certs.sh
        ./runtest --accurate --verbose --tls
    - name: module api test
      run: ./runtest-moduleapi --verbose --tls
    - name: sentinel tests
      run: ./runtest-sentinel
    - name: cluster tests
      run: ./runtest-cluster

  test-valgrind:
    runs-on: ubuntu-latest
    timeout-minutes: 14400
    steps:
    - uses: actions/checkout@v1
    - name: make
      run: make valgrind
    - name: test
      run: |
        sudo apt-get install tcl8.5 valgrind -y
        ./runtest --valgrind --verbose --clients 1
    - name: module api test
      run: ./runtest-moduleapi --valgrind --verbose --clients 1
