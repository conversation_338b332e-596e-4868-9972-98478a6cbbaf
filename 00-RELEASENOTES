Redis 6.0 release notes
=======================

--------------------------------------------------------------------------------
Upgrade urgency levels:

LOW:      No need to upgrade unless there are new features you want to use.
MODERATE: Program an upgrade of the server, but it's not urgent.
HIGH:     There is a critical bug that may affect a subset of users. Upgrade!
CRITICAL: There is a critical bug affecting MOST USERS. Upgrade ASAP.
SECURITY: There are security fixes in the release.
--------------------------------------------------------------------------------

================================================================================
Redis 6.0.6     Released Mon Jul 20 09:31:30 IDT 2020
================================================================================

Upgrade urgency MODERATE: several bugs with moderate impact are fixed here.

The most important issues are listed here:

* Fix crash when enabling CLIENT TRACKING with prefix
* EXEC always fails with EXECABORT and multi-state is cleared
* RESTORE ABSTTL won't store expired keys into the db
* redis-cli better handling of non-pritable key names
* TLS: Ignore client cert when tls-auth-clients off
* Tracking: fix invalidation message on flush
* Notify systemd on Sentinel startup
* Fix crash on a misuse of STRALGO
* Few fixes in module API
* Fix a few rare leaks (STRALGO error misuse, Sentinel)
* Fix a possible invalid access in defrag of scripts (unlikely to cause real harm)

New features:

* LPOS command to search in a list
* Use user+pass for MIGRATE in redis-cli and redis-benchmark in cluster mode
* redis-cli support TLS for --pipe, --rdb and --replica options
* TLS: Session caching configuration support

And this is the full list of commits:

Itamar Haber in commit 50548cafc:
 Adds SHA256SUM to redis-stable tarball upload
 1 file changed, 1 insertion(+)

yoav-steinberg in commit 3a4c6684f:
 Support passing stack allocated module strings to moduleCreateArgvFromUserFormat (#7528)
 1 file changed, 4 insertions(+), 1 deletion(-)

Luke Palmer in commit 2fd0b2bd6:
 Send null for invalidate on flush (#7469)
 1 file changed, 14 insertions(+), 10 deletions(-)

dmurnane in commit c3c81e1a8:
 Notify systemd on sentinel startup (#7168)
 1 file changed, 4 insertions(+)

Developer-Ecosystem-Engineering in commit e2770f29b:
 Add registers dump support for Apple silicon (#7453)
 1 file changed, 54 insertions(+), 2 deletions(-)

Wen Hui in commit b068eae97:
 correct error msg for num connections reaching maxclients in cluster mode (#7444)
 1 file changed, 2 insertions(+), 2 deletions(-)

WuYunlong in commit e6169ae5c:
 Fix command help for unexpected options (#7476)
 6 files changed, 20 insertions(+), 3 deletions(-)

WuYunlong in commit abf08fc02:
 Refactor RM_KeyType() by using macro. (#7486)
 1 file changed, 1 insertion(+), 1 deletion(-)

Oran Agra in commit 11b83076a:
 diskless master disconnect replicas when rdb child failed (#7518)
 1 file changed, 6 insertions(+), 5 deletions(-)

Oran Agra in commit 8f27f2f7d:
 redis-cli tests, fix valgrind timing issue (#7519)
 1 file changed, 1 insertion(+), 1 deletion(-)

WuYunlong in commit 180b588e8:
 Fix out of update help info in tcl tests. (#7516)
 1 file changed, 2 deletions(-)

Qu Chen in commit 417c60bdc:
 Replica always reports master's config epoch in CLUSTER NODES output. (#7235)
 1 file changed, 5 insertions(+), 1 deletion(-)

Oran Agra in commit 72a242419:
 RESTORE ABSTTL skip expired keys - leak (#7511)
 1 file changed, 1 insertion(+)

Oran Agra in commit 2ca45239f:
 fix recently added time sensitive tests failing with valgrind (#7512)
 2 files changed, 12 insertions(+), 6 deletions(-)

Oran Agra in commit 123dc8b21:
 runtest --stop pause stops before terminating the redis server (#7513)
 2 files changed, 8 insertions(+), 2 deletions(-)

Oran Agra in commit a6added45:
 update release scripts for new hosts, and CI to run more tests (#7480)
 5 files changed, 68 insertions(+), 26 deletions(-)

jimgreen2013 in commit cf4869f9e:
 fix description about ziplist, the code is ok (#6318)
 1 file changed, 2 insertions(+), 2 deletions(-)

马永泽 in commit d548f219b:
 fix benchmark in cluster mode fails to authenticate (#7488)
 1 file changed, 56 insertions(+), 40 deletions(-)

Abhishek Soni in commit e58eb7b89:
 fix: typo in CI job name (#7466)
 1 file changed, 1 insertion(+), 1 deletion(-)

Jiayuan Chen in commit 6def10a2b:
 Fix typo in deps README (#7500)
 1 file changed, 1 insertion(+), 1 deletion(-)

WuYunlong in commit 8af61afef:
 Add missing latency-monitor tcl test to test_helper.tcl. (#6782)
 1 file changed, 1 insertion(+)

Yossi Gottlieb in commit a419f400e:
 TLS: Session caching configuration support. (#7420)
 6 files changed, 56 insertions(+), 16 deletions(-)

Yossi Gottlieb in commit 2e4bb2667:
 TLS: Ignore client cert when tls-auth-clients off. (#7457)
 1 file changed, 1 insertion(+), 3 deletions(-)

James Hilliard in commit f0b1aee9e:
 Use pkg-config to properly detect libssl and libcrypto libraries (#7452)
 1 file changed, 15 insertions(+), 3 deletions(-)

Yossi Gottlieb in commit e92b99564:
 TLS: Add missing redis-cli options. (#7456)
 3 files changed, 166 insertions(+), 52 deletions(-)

Oran Agra in commit 1f3db5bf5:
 redis-cli --hotkeys fixed to handle non-printable key names
 1 file changed, 11 insertions(+), 5 deletions(-)

Oran Agra in commit c3044f369:
 redis-cli --bigkeys fixed to handle non-printable key names
 1 file changed, 24 insertions(+), 16 deletions(-)

Oran Agra in commit b3f75527b:
 RESTORE ABSTTL won't store expired keys into the db (#7472)
 4 files changed, 46 insertions(+), 16 deletions(-)

huangzhw in commit 6f87fc92f:
 defrag.c activeDefragSdsListAndDict when defrag sdsele, We can't use (#7492)
 1 file changed, 1 insertion(+), 1 deletion(-)

Oran Agra in commit d8e6a3e5b:
 skip a test that uses +inf on valgrind (#7440)
 1 file changed, 12 insertions(+), 9 deletions(-)

Oran Agra in commit 28fd1a110:
 stabilize tests that look for log lines (#7367)
 3 files changed, 33 insertions(+), 11 deletions(-)

Oran Agra in commit a513b4ed9:
 tests/valgrind: don't use debug restart (#7404)
 4 files changed, 114 insertions(+), 57 deletions(-)

Oran Agra in commit 70e72fc1b:
 change references to the github repo location (#7479)
 5 files changed, 7 insertions(+), 7 deletions(-)

zhaozhao.zz in commit c63e533cc:
 BITOP: propagate only when it really SET or DEL targetkey (#5783)
 1 file changed, 2 insertions(+), 1 deletion(-)

antirez in commit 31040ff54:
 Update comment to clarify change in #7398.
 1 file changed, 4 insertions(+), 1 deletion(-)

antirez in commit b605fe827:
 LPOS: option FIRST renamed RANK.
 2 files changed, 19 insertions(+), 19 deletions(-)

Dave Nielsen in commit 8deb24954:
 updated copyright year
 1 file changed, 1 insertion(+), 1 deletion(-)

Oran Agra in commit a61c2930c:
 EXEC always fails with EXECABORT and multi-state is cleared
 6 files changed, 204 insertions(+), 91 deletions(-)

antirez in commit 3c8041637:
 Include cluster.h for getClusterConnectionsCount().
 1 file changed, 1 insertion(+)

antirez in commit 5be673ee8:
 Fix BITFIELD i64 type handling, see #7417.
 1 file changed, 8 insertions(+), 6 deletions(-)

antirez in commit 5f289df9b:
 Clarify maxclients and cluster in conf. Remove myself too.
 2 files changed, 9 insertions(+), 1 deletion(-)

hwware in commit 000f928d6:
 fix memory leak in sentinel connection sharing
 1 file changed, 1 insertion(+)

chenhui0212 in commit d9a3c0171:
 Fix comments in function raxLowWalk of listpack.c
 1 file changed, 2 insertions(+), 2 deletions(-)

Tomasz Poradowski in commit 7526e4506:
 ensure SHUTDOWN_NOSAVE in Sentinel mode
 2 files changed, 9 insertions(+), 8 deletions(-)

chenhui0212 in commit 6487cbc33:
 fix comments in listpack.c
 1 file changed, 2 insertions(+), 2 deletions(-)

antirez in commit 69b66bfca:
 Use cluster connections too, to limit maxclients.
 3 files changed, 23 insertions(+), 8 deletions(-)

antirez in commit 5a960a033:
 Tracking: fix enableBcastTrackingForPrefix() invalid sdslen() call.
 1 file changed, 1 insertion(+), 1 deletion(-)

root in commit 1c2e50de3:
 cluster.c remove if of clusterSendFail in markNodeAsFailingIfNeeded
 1 file changed, 1 insertion(+), 1 deletion(-)

<EMAIL> in commit 040efb697:
 Fix RM_ScanKey module api not to return int encoded strings
 3 files changed, 24 insertions(+), 7 deletions(-)

antirez in commit 1b8b7941d:
 Fix LCS object type checking. Related to #7379.
 1 file changed, 17 insertions(+), 10 deletions(-)

hwware in commit 6b571b45a:
 fix memory leak
 1 file changed, 11 insertions(+), 12 deletions(-)

hwware in commit 674759062:
 fix server crash in STRALGO command
 1 file changed, 7 insertions(+)

Benjamin Sergeant in commit a05ffefdc:
 Update redis-cli.c
 1 file changed, 19 insertions(+), 6 deletions(-)

Jamie Scott in commit 870b63733:
 minor fix
 1 file changed, 2 insertions(+), 3 deletions(-)

================================================================================
Redis 6.0.5     Released Tue Jun 09 11:56:08 CEST 2020
================================================================================

Upgrade urgency MODERATE: several bugs with moderate impact are fixed here.

The most important issues are listed here:

* Fix handling of speical chars in ACL LOAD.
* Make Redis Cluster more robust about operation errors that may lead
  to two clusters to mix together.
* Revert the sendfile() implementation of RDB transfer. It causes some delay.
* Fix TLS certificate loading for chained certificates.
* Fix AOF rewirting of KEEPTTL SET option.
* Fix MULTI/EXEC behavior during -BUSY script errors.

And this is the full list of commits:

antirez in commit ee8dd01bb:
 Temporary fix for #7353 issue about EVAL during -BUSY.
 1 file changed, 9 insertions(+)

xhe in commit a4a856d53:
 return the correct proto version HELLO should return the current proto version, while the code hardcoded 3
 1 file changed, 1 insertion(+), 1 deletion(-)

Oran Agra in commit e2046b300:
 Don't queue commands in an already aborted MULTI state
 1 file changed, 7 insertions(+)

Oran Agra in commit b35fdf1de:
 Avoid rejecting WATCH / UNWATCH, like MULTI/EXEC/DISCARD
 1 file changed, 4 insertions(+), 2 deletions(-)

zhaozhao.zz in commit 1d7bf208c:
 AOF: append origin SET if no expire option
 2 files changed, 23 insertions(+), 8 deletions(-)

Oran Agra in commit 676445ad9:
 fix disconnectSlaves, to try to free each slave.
 1 file changed, 1 deletion(-)

zhaozhao.zz in commit 4846c0c8a:
 donot free protected client in freeClientsInAsyncFreeQueue
 1 file changed, 9 insertions(+), 3 deletions(-)

Oran Agra in commit f33de403e:
 fix pingoff  test race
 1 file changed, 1 insertion(+)

Kevin Fwu in commit 49af4d07e:
 Fix TLS certificate loading for chained certificates.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 329fddbda:
 Revert "Implements sendfile for redis."
 2 files changed, 2 insertions(+), 55 deletions(-)

antirez in commit 925a2cd5a:
 Revert "avoid using sendfile if tls-replication is enabled"
 1 file changed, 27 insertions(+), 34 deletions(-)

Liu Zhen in commit 84a7a9058:
 fix clusters mixing accidentally by gossip
 1 file changed, 10 insertions(+), 2 deletions(-)

antirez in commit cd63359a1:
 Fix handling of special chars in ACL LOAD.
 1 file changed, 8 insertions(+), 4 deletions(-)

================================================================================
Redis 6.0.4     Released Thu May 28 11:36:45 CEST 2020
================================================================================

Upgrade urgency CRITICAL: this release fixes a severe replication bug.

Redis 6.0.4 fixes a critical replication bug caused by a new feature introduced
in Redis 6. The feature, called "meaningful offset" and strongly wanted by
myself (antirez) was an improvement that avoided that masters were no longer
able, during a failover where they were demoted to replicas, to partially
synchronize with the new master. In short the feature was able to avoid full
synchronizations with RDB. How did it work? By trimming the replication backlog
of the final "PING" commands the master was sending in the replication channel:
this way the replication offset would no longer go "after" the one of the
promoted replica, allowing the master to just continue in the same replication
history, receiving only a small data difference.

However after the introduction of the feature we (the Redis core team) quickly
understood there was something wrong: the apparently harmless feature had
many bugs, and the last bug we discovered, after a joined effort of multiple
people, we were not even able to fully understand after fixing it. Enough was
enough, we decided that the complexity cost of this feature was too high.
So Redis 6.0.4 removes the feature entirely, and fixes the data corruption that
it was able to cause.

However there are two facts to take in mind.

Fact 1: Setups using chained replication, that means that certain replicas
are replicating from other replicas, up to Redis 6.0.3 can experience data
corruption. For chained replication we mean that:

    +--------+          +---------+         +-------------+
    | master |--------->| replica |-------->| sub-replica |
    +--------+          +---------+         +-------------+


People using chained replication SHOULD UPGRADE ASAP away from Redis 6.0.0,
6.0.1, 6.0.2 or 6.0.3 to Redis 6.0.4.

To be clear, people NOT using this setup, but having just replicas attached
directly to the master, SHOUDL NOT BE in danger of any problem. But we
are no longer confident on 6.0.x replication implementation complexities
so we suggest to upgrade to 6.0.4 to everybody using an older 6.0.3 release.
We just so far didn't find any bug that affects Redis 6.0.3 that does not
involve chained replication.

People starting with Redis 6.0.4 are fine. People with Redis 5 are fine.
People upgrading from Redis 5 to Redis 6.0.4 are fine.
TLDR: The problem is with users of 6.0.0, 6.0.1, 6.0.2, 6.0.3.

Fact 2: Upgrading from Redis 6.0.x to Redis 6.0.4, IF AND ONLY IF you
use chained replication, requires some extra care:

1. Once you attach your new Redis 6.0.4 instance as a replica of the current
   Redis 6.0.x master, you should wait for the first full synchronization,
   then you should promote it right away, if your setup involves chained
   replication. Don't give it the time to do a new partial synchronization
   in the case the link between the master and the replica  will break in
   the mean time.

2. As an additional care, you may want to set the replication ping period
   to a very large value (for instance 1000000) using the following command:

       CONFIG SET repl-ping-replica-period 1000000

   Note that if you do "1" with care, "2" is not needed.
   However if you do it, make sure to later restore it to its default:

       CONFIG SET repl-ping-replica-period 10

So this is the main change in Redis 6. Later we'll find a different way in
order to achieve what we wanted to achieve with the Meaningful Offset feature,
but without the same complexity.

Other changes in this release:

* PSYNC2 tests improved.
* Fix a rare active defrag edge case bug leading to stagnation
* Fix Redis 6 asserting at startup in 32 bit systems.
* Redis 6 32 bit is now added back to our testing environments.
* Fix server crash for STRALGO command,
* Implement sendfile for RDB transfer.
* TLS fixes.
* Make replication more resistant by disconnecting the master if we
  detect a protocol error. Basically we no longer accept inline protocol
  from the master.
* Other improvements in the tests.

Regards,
antirez

This is the full list of commits:

antirez in commit 59cd4c9f6:
 Test: take PSYNC2 test master timeout high during switch.
 1 file changed, 1 deletion(-)

antirez in commit 6c1bb7b19:
 Test: add the tracking unit as default.
 1 file changed, 1 insertion(+)

Oran Agra in commit 1aee695e5:
 tests: find_available_port start search from next port
 1 file changed, 12 insertions(+), 7 deletions(-)

Oran Agra in commit a2ae46352:
 tests: each test client work on a distinct port range
 5 files changed, 39 insertions(+), 27 deletions(-)

Oran Agra in commit 86e562d69:
 32bit CI needs to build modules correctly
 2 files changed, 7 insertions(+), 2 deletions(-)

Oran Agra in commit ab2984b1e:
 adjust revived meaningful offset tests
 1 file changed, 39 insertions(+), 20 deletions(-)

Oran Agra in commit 1ff5a222d:
 revive meaningful offset tests
 2 files changed, 213 insertions(+)

antirez in commit cc549b46a:
 Replication: showLatestBacklog() refactored out.
 3 files changed, 36 insertions(+), 25 deletions(-)

antirez in commit 377dd0515:
 Drop useless line from replicationCacheMaster().
 1 file changed, 2 deletions(-)

antirez in commit 3f8d113f1:
 Another meaningful offset test removed.
 1 file changed, 100 deletions(-)

antirez in commit d4541349d:
 Remove the PSYNC2 meaningful offset test.
 2 files changed, 113 deletions(-)

antirez in commit 2112a5702:
 Remove the meaningful offset feature.
 4 files changed, 10 insertions(+), 93 deletions(-)

antirez in commit d2eb6e0b4:
 Set a protocol error if master use the inline protocol.
 1 file changed, 17 insertions(+), 2 deletions(-)

Oran Agra in commit 9c1df3b76:
 daily CI test with tls
 1 file changed, 15 insertions(+)

Oran Agra in commit 115ed1911:
 avoid using sendfile if tls-replication is enabled
 1 file changed, 34 insertions(+), 27 deletions(-)

antirez in commit 11c748aac:
 Replication: log backlog creation event.
 1 file changed, 3 insertions(+)

antirez in commit 8f1013722:
 Test: PSYNC2 test can now show server logs.
 1 file changed, 88 insertions(+), 25 deletions(-)

antirez in commit 2e591fc4a:
 Clarify what is happening in PR #7320.
 1 file changed, 5 insertions(+), 1 deletion(-)

zhaozhao.zz in commit cbb51fb8f:
 PSYNC2: second_replid_offset should be real meaningful offset
 1 file changed, 3 insertions(+), 3 deletions(-)

Oran Agra in commit e0fc88b4d:
 add CI for 32bit build
 2 files changed, 34 insertions(+)

antirez in commit e3f864b5f:
 Make disconnectSlaves() synchronous in the base case.
 3 files changed, 20 insertions(+), 9 deletions(-)

ShooterIT in commit 8af1e513f:
 Implements sendfile for redis.
 2 files changed, 55 insertions(+), 2 deletions(-)

antirez in commit 3c21418cd:
 Fix #7306 less aggressively.
 2 files changed, 29 insertions(+), 17 deletions(-)

Madelyn Olson in commit e201f83ce:
 EAGAIN for tls during diskless load
 1 file changed, 4 insertions(+)

Qu Chen in commit 58fc456cb:
 Disconnect chained replicas when the replica performs PSYNC with the master always to avoid replication offset mismatch between master and chained replicas.
 2 files changed, 60 insertions(+), 3 deletions(-)

hwware in commit 3febc5c29:
 using moreargs variable
 1 file changed, 2 insertions(+), 2 deletions(-)

hwware in commit 8d6738559:
 fix server crash for STRALGO command
 1 file changed, 2 insertions(+), 2 deletions(-)

ShooterIT in commit 7a35eec54:
 Replace addDeferredMultiBulkLength with addReplyDeferredLen in comment
 1 file changed, 2 insertions(+), 2 deletions(-)

Yossi Gottlieb in commit f93e1417b:
 TLS: Improve tls-protocols clarity in redis.conf.
 1 file changed, 3 insertions(+), 2 deletions(-)

ShooterIT in commit d0c9e4454:
 Fix reply bytes calculation error
 1 file changed, 1 insertion(+), 1 deletion(-)

zhaozhao.zz in commit 1cde6a060:
 Tracking: flag CLIENT_TRACKING_BROKEN_REDIR when redir broken
 1 file changed, 1 insertion(+)

Oran Agra in commit 436be3498:
 fix a rare active defrag edge case bug leading to stagnation
 4 files changed, 146 insertions(+), 23 deletions(-)

Oran Agra in commit f9d2ffdc5:
 improve DEBUG MALLCTL to be able to write to write only fields.
 1 file changed, 27 insertions(+), 7 deletions(-)

hujie in commit d7968ee92:
 fix clear USER_FLAG_ALLCOMMANDS flag in acl
 1 file changed, 5 insertions(+), 4 deletions(-)

ShooterIT in commit a902e6b25:
 Redis Benchmark: generate random test data
 1 file changed, 12 insertions(+), 1 deletion(-)

hwware in commit 9564ed7c3:
 Redis-Benchmark: avoid potentical memmory leaking
 1 file changed, 1 insertion(+), 1 deletion(-)

WuYunlong in commit 2e4182743:
 Handle keys with hash tag when computing hash slot using tcl cluster client.
 1 file changed, 23 insertions(+), 2 deletions(-)

WuYunlong in commit eb2c8b2c6:
 Add a test to prove current tcl cluster client can not handle keys with hash tag.
 1 file changed, 7 insertions(+), 1 deletion(-)

ShooterIT in commit 928e6976b:
 Use dictSize to get the size of dict in dict.c
 1 file changed, 2 insertions(+), 2 deletions(-)

Madelyn Olson in commit cdcf5af5a:
 Converge hash validation for adding and removing
 1 file changed, 21 insertions(+), 14 deletions(-)

Benjamin Sergeant in commit e8b09d220:
 do not handle --cluster-yes for cluster fix mode
 1 file changed, 16 insertions(+), 7 deletions(-)

Benjamin Sergeant in commit 57b4fb0d8:
 fix typo ...
 1 file changed, 1 insertion(+), 1 deletion(-)

Benjamin Sergeant in commit 29f25e411:
 Redis-cli 6.0.1 `--cluster-yes` doesn't work (fix #7246)
 1 file changed, 5 insertions(+), 1 deletion(-)

Oran Agra in commit 00d8b92b8:
 fix valgrind test failure in replication test
 1 file changed, 1 insertion(+), 1 deletion(-)

Oran Agra in commit 5e17e6276:
 add regression test for the race in #7205
 1 file changed, 52 insertions(+)

antirez in commit 96e7c011e:
 Improve the PSYNC2 test reliability.
 1 file changed, 33 insertions(+), 15 deletions(-)

================================================================================
Redis 6.0.3     Released Sat May 16 18:10:21 CEST 2020
================================================================================

Upgrade urgency CRITICAL: a crash introduced in 6.0.2 is now fixed.

1eab62f7e Remove the client from CLOSE_ASAP list before caching the master.

================================================================================
Redis 6.0.2     Released Fri May 15 22:24:36 CEST 2020
================================================================================

Upgrade urgency MODERATE: many not critical bugfixes in different areas.
                          Critical fix to client side caching when
                          keys are evicted from the tracking table but
                          no notifications are sent.

The following are the most serious fix:

* XPENDING should not update consumer's seen-time
* optimize memory usage of deferred replies - fixed
* Fix CRC64 initialization outside the Redis server itself.
* stringmatchlen() should not expect null terminated strings.
* Cluster nodes availability checks improved when there is
  high Pub/Sub load on the cluster bus.
* Redis Benchmark: Fix coredump because of double free
* Tracking: send eviction messages when evicting entries.
* rax.c updated from upstream antirez/rax.
* fix redis 6.0 not freeing closed connections during loading.

New features:

* Support setcpuaffinity on linux/bsd
* Client Side Caching: Add Tracking Prefix Number Stats in Server Info
* Add --user argument to redis-benchmark.c (ACL)

Full list of commits:

Yossi Gottlieb in commit 16ba33c05:
 TLS: Fix test failures on recent Debian/Ubuntu.
 1 file changed, 20 deletions(-)

Yossi Gottlieb in commit 77ae66930:
 TLS: Add crypto locks for older OpenSSL support.
 1 file changed, 45 insertions(+)

David Carlier in commit 389697988:
 NetBSD build update.
 3 files changed, 30 insertions(+), 1 deletion(-)

Madelyn Olson in commit 2435341d7:
 Added a refcount on timer events to prevent deletion of recursive timer calls
 2 files changed, 12 insertions(+)

antirez in commit 80c906bd3:
 Cache master without checking of deferred close flags.
 3 files changed, 11 insertions(+), 8 deletions(-)

antirez in commit 74249be4a:
 Track events processed while blocked globally.
 5 files changed, 32 insertions(+), 17 deletions(-)

antirez in commit 8bf660af9:
 Some rework of #7234.
 4 files changed, 77 insertions(+), 65 deletions(-)

Oran Agra in commit 9da134cd8:
 fix redis 6.0 not freeing closed connections during loading.
 3 files changed, 133 insertions(+), 58 deletions(-)

antirez in commit f7f219a13:
 Regression test for #7249.
 1 file changed, 22 insertions(+)

antirez in commit 693629585:
 rax.c updated from upstream antirez/rax.
 1 file changed, 4 insertions(+), 2 deletions(-)

antirez in commit e3b5648df:
 Tracking: send eviction messages when evicting entries.
 2 files changed, 29 insertions(+), 12 deletions(-)

Oran Agra in commit 5c41802d5:
 fix unstable replication test
 1 file changed, 2 insertions(+), 2 deletions(-)

ShooterIT in commit a23cdbb94:
 Redis Benchmark: Fix coredump because of double free
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 1276058ea:
 Cluster: clarify we always resolve the sender.
 1 file changed, 3 insertions(+), 1 deletion(-)

antirez in commit 002fcde3d:
 Cluster: refactor ping/data delay handling.
 1 file changed, 13 insertions(+), 11 deletions(-)

antirez in commit 960186a71:
 Cluster: introduce data_received field.
 2 files changed, 27 insertions(+), 10 deletions(-)

antirez in commit 3672875b4:
 stringmatchlen() should not expect null terminated strings.
 1 file changed, 2 insertions(+), 2 deletions(-)

Brad Dunbar in commit 24e12641d:
 Remove unreachable branch.
 1 file changed, 2 deletions(-)

hwware in commit c7edffbd5:
 add jemalloc-bg-thread config in redis conf
 1 file changed, 3 insertions(+)

hwware in commit 8a9c84f4a:
 add include guard for lolwut.h
 1 file changed, 6 insertions(+)

antirez in commit cb683a84f:
 Don't propagate spurious MULTI on DEBUG LOADAOF.
 2 files changed, 6 insertions(+), 3 deletions(-)

antirez in commit 84d9766d6:
 Dump recent backlog on master query generating errors.
 1 file changed, 29 insertions(+)

Titouan Christophe in commit ec1e106ec:
 make struct user anonymous (only typedefed)
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit e48c37316:
 Test: --dont-clean should do first cleanup.
 1 file changed, 2 insertions(+), 5 deletions(-)

Benjamin Sergeant in commit 1e561cfaa:
 Add --user argument to redis-benchmark.c (ACL)
 1 file changed, 15 insertions(+), 2 deletions(-)

antirez in commit d1af82a88:
 Drop not needed part from #7194.
 1 file changed, 1 insertion(+), 1 deletion(-)

Muhammad Zahalqa in commit 897a360d0:
 Fix compiler warnings on function rev(unsigned long)
 1 file changed, 3 insertions(+), 3 deletions(-)

antirez in commit ac316d8cc:
 Move CRC64 initialization in main().
 2 files changed, 1 insertion(+), 4 deletions(-)

antirez in commit fc7bc3204:
 Fix CRC64 initialization outside the Redis server itself.
 1 file changed, 3 insertions(+)

hwware in commit a6e55c096:
 Client Side Caching: Add Tracking Prefix Number Stats in Server Info
 3 files changed, 8 insertions(+)

antirez in commit b062fd523:
 Fix NetBSD build by fixing redis_set_thread_title() support.
 1 file changed, 4 insertions(+), 1 deletion(-)

antirez in commit 4efb25d9c:
 Rework a bit the documentation for CPU pinning.
 2 files changed, 18 insertions(+), 8 deletions(-)

zhenwei pi in commit d6436eb7c:
 Support setcpuaffinity on linux/bsd
 12 files changed, 180 insertions(+), 1 deletion(-)

Guy Benoish in commit 3a441c7d9:
 XPENDING should not update consumer's seen-time
 4 files changed, 33 insertions(+), 20 deletions(-)

Oran Agra in commit 75addb4fe:
 optimize memory usage of deferred replies - fixed
 1 file changed, 29 insertions(+)

Deliang Yang in commit c57d9146f:
 reformat code
 1 file changed, 1 insertion(+), 1 deletion(-)

Oran Agra in commit 3d3861dd8:
 add daily github actions with libc malloc and valgrind
 5 files changed, 106 insertions(+), 18 deletions(-)


================================================================================
Redis 6.0.1     Released Sat May 02 00:06:07 CEST 2020
================================================================================

Upgrade urgency HIGH: This release fixes a crash when builiding against
                      Libc malloc.

Here we revert 8110ba888, an optimization that causes a crash due to a
bug in the code. It does not happen with the default allocator because of
differences between Jemalloc and libc malloc, so this escaped all our
testing but was reported by a user. We'll add back the original optimization
that was reverted here later, after checking what happens: it is not a
critical optimization.

The other commits are minor stuff:

antirez in commit db73d0998:
 Cast printf() argument to the format specifier.
 1 file changed, 3 insertions(+), 1 deletion(-)

antirez in commit 7c0fe7271:
 Revert "optimize memory usage of deferred replies"
 1 file changed, 31 deletions(-)

antirez in commit 8fe25edc7:
 Save a call to stopThreadedIOIfNeeded() for the base case.
 1 file changed, 3 insertions(+), 3 deletions(-)

================================================================================
Redis 6.0.0 GA  Released Thu Apr 30 14:55:02 CEST 2020
================================================================================

Upgrade urgency CRITICAL: many bugs fixed compared to the last release
                          candidate. Better to upgrade if you see things
                          affecting your environment in the changelog.

Hi all, finally we have Redis 6.0.0 GA! Enjoy this new Redis release.
Most of the documentation was updated today so that you can likely
find what you are looking for about the new features at redis.io.
This is the list of what changed compared to the previoius release candidate:

* XCLAIM AOF/replicas propagation fixed.
* Client side caching: new NOLOOP option to avoid getting notified about
  changes performed by ourselves.
* ACL GENPASS now uses HMAC-SHA256 and have an optional "bits" argument.
  It means you can use it as a general purpose "secure random strings"
  primitive!
* Cluster "SLOTS" subcommand memory optimization.
* The LCS command is now a subcommand of STRALGO.
* Meaningful offset for replicas as well. More successful partial
  resynchronizations.
* Optimize memory usage of deferred replies.
* Faster CRC64 algorithm for faster RDB loading.
* XINFO STREAM FULL, a new subcommand to get the whole stream state.
* CLIENT KILL USER <username>.
* MIGRATE AUTH2 option, for ACL style authentication support.
* Other random bugfixes.

Enjoy Redis 6! :-)
Goodbye antirez

List of commits in this release:

antirez in commit 1f9b82bd5:
 Update help.h again before Redis 6 GA.
 1 file changed, 17 insertions(+), 12 deletions(-)

antirez in commit 3fcffe7d0:
 redis-cli: fix hints with subcommands.
 1 file changed, 2 insertions(+), 1 deletion(-)

antirez in commit 455d8a05c:
 redis-cli command help updated.
 1 file changed, 165 insertions(+), 25 deletions(-)

zhaozhao.zz in commit 70287bbc9:
 lazyfree & eviction: record latency generated by lazyfree eviction
 1 file changed, 18 insertions(+), 13 deletions(-)

antirez in commit 7be21139a:
 MIGRATE AUTH2 for ACL support.
 1 file changed, 19 insertions(+), 5 deletions(-)

antirez in commit e1ee1a49d:
 CLIENT KILL USER <username>.
 1 file changed, 11 insertions(+)

antirez in commit d56f058c0:
 Fix tracking table max keys option in redis.conf.
 1 file changed, 12 insertions(+), 9 deletions(-)

antirez in commit 96dd5fc93:
 redis-cli: safer cluster fix with unreachalbe masters.
 1 file changed, 26 insertions(+), 1 deletion(-)

antirez in commit 5b59d9c5d:
 redis-cli: simplify cluster nodes coverage display.
 1 file changed, 10 insertions(+), 17 deletions(-)

antirez in commit c163d4add:
 redis-cli: try to make clusterManagerFixOpenSlot() more readable.
 1 file changed, 25 insertions(+), 6 deletions(-)

Guy Benoish in commit aab74b715:
 XINFO STREAM FULL should have a default COUNT of 10
 1 file changed, 8 insertions(+), 4 deletions(-)

antirez in commit 606134f9d:
 Comment clearly why we moved some code in #6623.
 1 file changed, 4 insertions(+), 1 deletion(-)

srzhao in commit ee627bb66:
 fix pipelined WAIT performance issue.
 1 file changed, 13 insertions(+), 13 deletions(-)

antirez in commit 47b8a7f9b:
 Fix create-cluster BIN_PATH.
 1 file changed, 1 insertion(+), 1 deletion(-)

Guy Benoish in commit 6c0bc608a:
 Extend XINFO STREAM output
 2 files changed, 226 insertions(+), 34 deletions(-)

hwware in commit 5bfc18950:
 Fix not used marco in cluster.c
 1 file changed, 1 insertion(+), 1 deletion(-)

Itamar Haber in commit 56d628f85:
 Update create-cluster
 1 file changed, 1 insertion(+), 1 deletion(-)

Itamar Haber in commit cac9d7cf7:
 Adds `BIN_PATH` to create-cluster
 1 file changed, 8 insertions(+), 6 deletions(-)

Oran Agra in commit b712fba17:
 hickup, re-fix dictEncObjKeyCompare
 1 file changed, 4 insertions(+), 4 deletions(-)

Oran Agra in commit ea63aea72:
 fix loading race in psync2 tests
 3 files changed, 15 insertions(+), 1 deletion(-)

antirez in commit 64e588bfa:
 Rework comment in dictEncObjKeyCompare().
 1 file changed, 8 insertions(+), 9 deletions(-)

Oran Agra in commit 0d1e8c93b:
 allow dictFind using static robj
 1 file changed, 9 insertions(+), 4 deletions(-)

Madelyn Olson in commit a1bed447b:
 Added crcspeed library
 2 files changed, 341 insertions(+)

Madelyn Olson in commit a75fa3aad:
 Made crc64 test consistent
 1 file changed, 3 insertions(+), 2 deletions(-)

Madelyn Olson in commit 52c75e9db:
 Implemented CRC64 based on slice by 4
 5 files changed, 124 insertions(+), 157 deletions(-)

Oran Agra in commit 8110ba888:
 optimize memory usage of deferred replies
 1 file changed, 31 insertions(+)

Oran Agra in commit e4d2bb62b:
 Keep track of meaningful replication offset in replicas too
 5 files changed, 212 insertions(+), 92 deletions(-)

antirez in commit fea9788cc:
 Fix STRALGO command flags.
 1 file changed, 1 insertion(+), 1 deletion(-)

Dave-in-lafayette in commit 2144047e1:
 fix for unintended crash during panic response
 1 file changed, 1 insertion(+), 1 deletion(-)

Guy Benoish in commit 43329c9b6:
 Add the stream tag to XSETID tests
 1 file changed, 1 insertion(+), 1 deletion(-)

Dave-in-lafayette in commit 1e17d3de7:
 fix for crash during panic before all threads are up
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 3722f89f4:
 LCS -> STRALGO LCS.
 4 files changed, 28 insertions(+), 15 deletions(-)

antirez in commit 373ae6061:
 Also use propagate() in streamPropagateGroupID().
 1 file changed, 11 insertions(+), 1 deletion(-)

yanhui13 in commit f03f1fad6:
 add tcl test for cluster slots
 1 file changed, 44 insertions(+)

yanhui13 in commit 374ffdf1c:
 optimize the output of cluster slots
 1 file changed, 7 insertions(+), 4 deletions(-)

antirez in commit 4db38d2ef:
 Minor aesthetic changes to #7135.
 1 file changed, 5 insertions(+), 7 deletions(-)

Valentino Geron in commit f0a261448:
 XREADGROUP with NOACK should propagate only one XGROUP SETID command
 1 file changed, 13 insertions(+), 7 deletions(-)

antirez in commit fbdef6a9b:
 ACL: re-enable command execution of disabled users.
 1 file changed, 4 deletions(-)

antirez in commit 05a41da75:
 getRandomBytes(): use HMAC-SHA256.
 1 file changed, 30 insertions(+), 10 deletions(-)

antirez in commit 345c3768d:
 ACL GENPASS: take number of bits as argument.
 1 file changed, 21 insertions(+), 6 deletions(-)

antirez in commit 639c8a1d9:
 ACL GENPASS: emit 256 bits instead of 128.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 321acea03:
 ACL: deny commands execution of disabled users.
 1 file changed, 4 insertions(+)

Theo Buehler in commit b0920e6e8:
 TLS: Fix build with SSL_OP_NO_CLIENT_RENEGOTIATION
 1 file changed, 1 insertion(+), 1 deletion(-)

Yossi Gottlieb in commit 149b658b5:
 TLS: Fix build on older verisons of OpenSSL.
 1 file changed, 2 insertions(+)

antirez in commit 06917e581:
 Tracking: test expired keys notifications.
 1 file changed, 13 insertions(+)

antirez in commit e434b2ce4:
 Tracking: NOLOOP tests.
 1 file changed, 32 insertions(+)

antirez in commit f3a172887:
 Tracking: signal key as modified when evicting.
 1 file changed, 1 insertion(+)

antirez in commit e63bb7ec8:
 Tracking: NOLOOP further implementation and fixes.
 2 files changed, 21 insertions(+), 6 deletions(-)

antirez in commit 6791ff052:
 Tracking: NOLOOP internals implementation.
 17 files changed, 174 insertions(+), 112 deletions(-)

antirez in commit 725b8cc68:
 Implement redis_set_thread_title for MacOS.
 1 file changed, 6 insertions(+)

zhenwei pi in commit 3575b8706:
 Threaded IO: set thread name for redis-server
 3 files changed, 28 insertions(+)

antirez in commit a76c67578:
 Sentinel: small refactoring of sentinelCollectTerminatedScripts().
 1 file changed, 1 insertion(+), 2 deletions(-)

omg-by in commit 3a27064c4:
 fix(sentinel): sentinel.running_scripts will always increase more times and not reset
 1 file changed, 1 insertion(+)

antirez in commit 5c4c73e2c:
 A few comments and name changes for #7103.
 1 file changed, 13 insertions(+), 4 deletions(-)

Oran Agra in commit 6148f9493:
 testsuite run the defrag latency test solo
 3 files changed, 42 insertions(+), 2 deletions(-)

Jamie Scott in commit 51d3012d4:
 Adding acllog-max-len to Redis.conf
 1 file changed, 9 insertions(+)

antirez in commit c39f16c42:
 Fix XCLAIM propagation in AOF/replicas for blocking XREADGROUP.
 2 files changed, 8 insertions(+), 3 deletions(-)

================================================================================
Redis 6.0-rc4     Released Thu Apr 16 16:10:35 CEST 2020
================================================================================

Upgrade urgency LOW: If you are using RC3 without issues, don't rush.

Hi all, this the latest release candidate of Redis 6. This is likely to
be very similar to what you'll see in Redis 6 GA. Please test it and
report any issue :-)

Main changes in this release:

    * Big INFO speedup when using a lot of of clients.
    * Big speedup on all the blocking commands: now blocking
      on the same key is O(1) instead of being O(N).
    * Stale replicas now allow MULTI/EXEC.
    * New command: LCS (Longest Common Subsequence).
    * Add a new configuration to make DEL like UNLINK.
    * RDB loading speedup.
    * Many bugs fixed (see the commit messages at the end of this node)

See you in 14 days for Redis 6 GA.

List of commits:

antirez in commit 9f594e243:
 Update SDS to latest version.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 48781dd95:
 RESP3: fix HELLO map len in Sentinel mode.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 371ab0cff:
 Don't allow empty spaces in ACL usernames.
 1 file changed, 36 insertions(+), 8 deletions(-)

antirez in commit b86140ac5:
 Don't allow empty spaces in ACL key patterns.
 1 file changed, 12 insertions(+), 1 deletion(-)

liumiuyong in commit a7ee3c3e7:
 FIX: truncate max/min longitude,latitude related geo_point (ex:  {180, 85.05112878} )
 1 file changed, 4 insertions(+)

Guy Benoish in commit e5b9eb817:
 Typo in getTimeoutFromObjectOrReply's error reply
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 0f31bb5c1:
 Fix HELLO reply in Sentinel mode, see #6160.
 1 file changed, 1 insertion(+), 1 deletion(-)

hwware in commit b92d9a895:
 fix spelling in acl.c
 1 file changed, 2 insertions(+), 2 deletions(-)

antirez in commit 8f896e57a:
 Fix zsetAdd() top comment spelling.
 1 file changed, 3 insertions(+), 3 deletions(-)

hayleeliu in commit 8f5157058:
 fix spelling mistake in bitops.c
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit ddeda9ceb:
 Fix function names in zslDeleteNode() top comment.
 1 file changed, 2 insertions(+), 1 deletion(-)

antirez in commit bde1f0a8e:
 RESP3: change streams items from maps to arrays.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit bec68bff2:
 Use the special static refcount for stack objects.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 0f239e51b:
 RDB: refactor some RDB loading code into dbAddRDBLoad().
 3 files changed, 22 insertions(+), 4 deletions(-)

antirez in commit f855db61b:
 incrRefCount(): abort on statically allocated object.
 2 files changed, 12 insertions(+), 2 deletions(-)

antirez in commit 23094ba01:
 More powerful DEBUG RELOAD.
 3 files changed, 55 insertions(+), 16 deletions(-)

antirez in commit 8161a7a3e:
 RDB: clarify a condition in rdbLoadRio().
 2 files changed, 9 insertions(+), 2 deletions(-)

antirez in commit 61b153073:
 RDB: load files faster avoiding useless free+realloc.
 7 files changed, 40 insertions(+), 28 deletions(-)

antirez in commit 414debfd0:
 Speedup: unblock clients on keys in O(1).
 4 files changed, 50 insertions(+), 23 deletions(-)

antirez in commit cbcd07777:
 Fix ACL HELP table missing comma.
 1 file changed, 12 insertions(+), 12 deletions(-)

mymilkbottles in commit 2437455f2:
 Judge the log level in advance
 1 file changed, 1 insertion(+)

antirez in commit 35c64b898:
 Speedup INFO by counting client memory incrementally.
 4 files changed, 52 insertions(+), 26 deletions(-)

qetu3790 in commit c3ac71748:
 fix comments about RESIZE DB opcode in rdb.c
 1 file changed, 1 insertion(+), 4 deletions(-)

antirez in commit c8dbcff9d:
 Clarify redis.conf comment about lazyfree-lazy-user-del.
 1 file changed, 9 insertions(+), 5 deletions(-)

zhaozhao.zz in commit abd5156f2:
 lazyfree: add a new configuration lazyfree-lazy-user-del
 4 files changed, 7 insertions(+), 2 deletions(-)

antirez in commit 5719b3054:
 LCS: more tests.
 1 file changed, 8 insertions(+)

antirez in commit c89e1f293:
 LCS: allow KEYS / STRINGS to be anywhere.
 1 file changed, 6 deletions(-)

antirez in commit 0b16f8d44:
 LCS tests.
 1 file changed, 22 insertions(+)

antirez in commit 9254a805d:
 LCS: get rid of STOREIDX option. Fix get keys helper.
 2 files changed, 20 insertions(+), 21 deletions(-)

antirez in commit a4c490703:
 LCS: fix stale comment.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit cb92c23de:
 LCS: output LCS len as well in IDX mode.
 1 file changed, 6 insertions(+), 1 deletion(-)

antirez in commit 56a52e804:
 LCS: MINMATCHLEN and WITHMATCHLEN options.
 1 file changed, 24 insertions(+), 11 deletions(-)

antirez in commit ebb09a5c3:
 LCS: 7x speedup by accessing the array with better locality.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit a9f8a8cba:
 LCS: implement KEYS option.
 1 file changed, 18 insertions(+), 2 deletions(-)

antirez in commit 4aa24e62a:
 LCS: other fixes to range emission.
 1 file changed, 20 insertions(+), 16 deletions(-)

antirez in commit 2b67b6b87:
 LCS: fix emission of last range starting at index 0.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 420aac727:
 LCS: implement range indexes option.
 1 file changed, 59 insertions(+), 9 deletions(-)

antirez in commit a518a9a76:
 LCS: initial functionality implemented.
 4 files changed, 156 insertions(+), 1 deletion(-)

srzhao in commit 026cc11b0:
 Check OOM at script start to get stable lua OOM state.
 3 files changed, 11 insertions(+), 4 deletions(-)

Oran Agra in commit 02b594f6a:
 diffrent fix for runtest --host --port
 2 files changed, 13 insertions(+), 13 deletions(-)

Guy Benoish in commit f695d1830:
 Try to fix time-sensitive tests in blockonkey.tcl
 1 file changed, 54 insertions(+), 1 deletion(-)

Guy Benoish in commit 0e42cfc36:
 Use __attribute__ only if __GNUC__ is defined
 1 file changed, 12 insertions(+), 3 deletions(-)

Guy Benoish in commit 91ed9b3c4:
 Modules: Perform printf-like format checks in variadic API
 1 file changed, 3 insertions(+), 3 deletions(-)

Valentino Geron in commit 3e0d20962:
 XREAD and XREADGROUP should not be allowed from scripts when BLOCK option is being used
 3 files changed, 18 insertions(+), 2 deletions(-)

Guy Benoish in commit 240094c9b:
 Stale replica should allow MULTI/EXEC
 1 file changed, 3 insertions(+), 3 deletions(-)

Xudong Zhang in commit 209f3a1eb:
 fix integer overflow
 1 file changed, 2 insertions(+), 2 deletions(-)

Guy Benoish in commit 024c380b9:
 Fix no-negative-zero test
 1 file changed, 1 insertion(+)

Oran Agra in commit a38ff404b:
 modules don't signalModifiedKey in setKey() since that's done (optionally) in RM_CloseKey
 4 files changed, 8 insertions(+), 8 deletions(-)

Oran Agra in commit 814874d68:
 change CI to build and run the module api tests
 1 file changed, 2 insertions(+)

Oran Agra in commit 061616c1b:
 fix possible warning on incomplete struct init
 1 file changed, 1 insertion(+), 1 deletion(-)

Guy Benoish in commit 7764996be:
 Make sure Redis does not reply with negative zero
 2 files changed, 10 insertions(+)

Guy Benoish in commit eba28e2ce:
 DEBUG OBJECT should pass keyname to module when loading
 3 files changed, 4 insertions(+), 4 deletions(-)

David Carlier in commit 15c9e79a7:
 debug, dump registers on arm too.
 1 file changed, 55 insertions(+), 27 deletions(-)

hwware in commit cd2b5df97:
 fix spelling in cluster.c
 1 file changed, 1 insertion(+), 1 deletion(-)

Valentino Geron in commit 8cdc153f5:
 XACK should be executed in a "all or nothing" fashion.
 2 files changed, 23 insertions(+), 1 deletion(-)

hwware in commit b35407fa7:
 add check for not switching between optin optout mode directly
 1 file changed, 12 insertions(+), 1 deletion(-)

hwware in commit 4395889c9:
 add check for not providing both optin optout flag
 1 file changed, 8 insertions(+)

Guy Benoish in commit 1907e0f18:
 PERSIST should notify a keyspace event
 1 file changed, 1 insertion(+)

Guy Benoish in commit c35a53169:
 streamReplyWithRange: Redundant XSETIDs to replica
 1 file changed, 2 insertions(+), 1 deletion(-)

antirez in commit 6fe66e096:
 Simplify comment in moduleTryServeClientBlockedOnKey().
 1 file changed, 3 insertions(+), 12 deletions(-)

Guy Benoish in commit 193fc241c:
 Fix memory corruption in moduleHandleBlockedClients
 3 files changed, 149 insertions(+), 46 deletions(-)

================================================================================
Redis 6.0-rc3     Released Tue Mar 31 17:42:39 CEST 2020
================================================================================

Upgrade urgency CRITICAL: A connection management bug introduced with the
                          SSL implementation can crash Redis easily.

Dear users, this is a list of the major changes in this release, please check 
the list of commits for detail:

* Fix crash due to refactoring for SSL, for the connection code.
* Precise timeouts for blocking commands. Now the timeouts have HZ
  resolution regardless of the number of connected clinets. New timeouts
  are stored in a radix tree and sorted by expire time.
* Fix rare crash when resizing the event loop because of CONFIG maxclients.
* Fix systemd readiness after successful partial resync.
* Redis-cli ask password mode to be prompted at startup (for additional safety).
* Keyspace notifications added to MIGRATE / RESTORE.
* Threaded I/O bugs fixed.
* Implement new ACL style AUTH in Sentinel.
* Make 'requirepass' more backward compatible with Redis <= 5.
* ACL: Handle default user as disabled if it's off regardless of "nopass".
* Fix a potential inconsistency when upgrading an instance in Redis Cluster
  and restarting it. The instance will act as a replica but will actually be
  set as a master immediately. However the choice of what to do with already
  expired keys, on loading, was made from the POV of replicas.
* Abort transactions after -READONLY error.
* Many different fixes to module APIs.
* BITFIELD_RO added to call the command on read only replicas.
* PSYNC2: meaningful offset implementation. Allow the disconnected master
  that is still sending PINGs to replicas, to be able to successfully
  PSYNC incrementally to new slaves, discarding the last part of the
  replication backlog consisting only of PINGs.
* Fix pipelined MULTI/EXEC during Lua scripts are in BUSY state.
* Re-fix propagation API in modules, broken again after other changes.

antirez in commit ef1b1f01:
 cast raxSize() to avoid warning with format spec.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 9f347fab:
 Minor changes to #7037.
 2 files changed, 14 insertions(+), 5 deletions(-)

Guy Benoish in commit a509400d:
 Modules: Test MULTI/EXEC replication of RM_Replicate
 6 files changed, 49 insertions(+), 9 deletions(-)

Guy Benoish in commit 805c8c94:
 RENAME can unblock XREADGROUP
 3 files changed, 25 insertions(+), 1 deletion(-)

antirez in commit 97b80b57:
 Fix the propagate Tcl test after module changes.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 4f6b6b80:
 Modify the propagate unit test to show more cases.
 1 file changed, 30 insertions(+), 2 deletions(-)

antirez in commit 616b1cb7:
 Fix module commands propagation double MULTI bug.
 4 files changed, 25 insertions(+), 8 deletions(-)

antirez in commit 08fdef4b:
 Fix RM_Call() stale comment due to cut&paste.
 1 file changed, 1 insertion(+), 3 deletions(-)

OMG-By in commit 26b79ca1:
 fix: dict.c->dictResize()->minimal  type
 1 file changed, 1 insertion(+), 1 deletion(-)

zhaozhao.zz in commit fa418637:
 PSYNC2: reset backlog_idx and master_repl_offset correctly
 1 file changed, 10 insertions(+), 5 deletions(-)

antirez in commit bbbc80ac:
 Precise timeouts: reference client pointer directly.
 1 file changed, 13 insertions(+), 16 deletions(-)

antirez in commit c3b268a0:
 timeout.c created: move client timeouts code there.
 5 files changed, 198 insertions(+), 167 deletions(-)

Oran Agra in commit 0f7dfc37:
 AOFRW on an empty stream created with MKSTREAM loads badkly
 2 files changed, 15 insertions(+), 1 deletion(-)

antirez in commit 67643ead:
 Precise timeouts: cleaup the table on unblock.
 3 files changed, 21 insertions(+), 2 deletions(-)

antirez in commit ad94066e:
 Precise timeouts: fix comments after functional change.
 2 files changed, 6 insertions(+), 6 deletions(-)

antirez in commit a443ec2e:
 Precise timeouts: use only radix tree for timeouts.
 3 files changed, 15 insertions(+), 38 deletions(-)

antirez in commit 6862fd70:
 Precise timeouts: fast exit for clientsHandleShortTimeout().
 1 file changed, 1 insertion(+)

antirez in commit 30f1df8c:
 Precise timeouts: fix bugs in initial implementation.
 2 files changed, 5 insertions(+), 1 deletion(-)

antirez in commit 7add0f24:
 Precise timeouts: working initial implementation.
 3 files changed, 110 insertions(+), 28 deletions(-)

antirez in commit 9d6d1779:
 Precise timeouts: refactor unblocking on timeout.
 2 files changed, 33 insertions(+), 13 deletions(-)

antirez in commit 316a8f15:
 PSYNC2: fix backlog_idx when adjusting for meaningful offset
 1 file changed, 3 insertions(+)

伯成 in commit 11db53f8:
 Boost up performance for redis PUB-SUB patterns matching
 3 files changed, 43 insertions(+), 11 deletions(-)

antirez in commit e257f121:
 PSYNC2: meaningful offset test.
 2 files changed, 62 insertions(+)

antirez in commit 5f72f696:
 PSYNC2: meaningful offset implemented.
 3 files changed, 40 insertions(+), 1 deletion(-)

antirez in commit 8caa2714:
 Explain why we allow transactions in -BUSY state.
 1 file changed, 9 insertions(+), 2 deletions(-)

Oran Agra in commit e43cd831:
 MULTI/EXEC during LUA script timeout are messed up
 2 files changed, 73 insertions(+)

antirez in commit 34b89832:
 Improve comments of replicationCacheMasterUsingMyself().
 1 file changed, 6 insertions(+), 1 deletion(-)

antirez in commit 70a98a43:
 Fix BITFIELD_RO test.
 2 files changed, 5 insertions(+), 5 deletions(-)

antirez in commit 8783304a:
 Abort transactions after -READONLY error. Fix #7014.
 1 file changed, 1 insertion(+)

antirez in commit ec9cf002:
 Minor changes to BITFIELD_RO PR #6951.
 1 file changed, 9 insertions(+), 6 deletions(-)

bodong.ybd in commit b3e4abf0:
 Added BITFIELD_RO variants for read-only operations.
 4 files changed, 54 insertions(+), 1 deletion(-)

antirez in commit 50f8f950:
 Modules: updated function doc after #7003.
 1 file changed, 6 insertions(+), 1 deletion(-)

Guy Benoish in commit f2f3dc5e:
 Allow RM_GetContextFlags to work with ctx==NULL
 1 file changed, 16 insertions(+), 14 deletions(-)

hwware in commit eb808879:
 fix potentical memory leak in redis-cli
 1 file changed, 2 insertions(+)

Yossi Gottlieb in commit cdcab0e8:
 Fix crashes related to failed/rejected accepts.
 1 file changed, 6 insertions(+), 5 deletions(-)

Yossi Gottlieb in commit 50dcd9f9:
 Cluster: fix misleading accept errors.
 1 file changed, 4 insertions(+), 3 deletions(-)

Yossi Gottlieb in commit 87dbd8f5:
 Conns: Fix connClose() / connAccept() behavior.
 3 files changed, 48 insertions(+), 32 deletions(-)

hwware in commit 81e8686c:
 remove redundant Semicolon
 1 file changed, 1 insertion(+), 1 deletion(-)

hwware in commit c7524a7e:
 clean CLIENT_TRACKING_CACHING flag when disabled caching
 1 file changed, 1 insertion(+), 1 deletion(-)

hwware in commit 2dd1ca6a:
 add missing commands in cluster help
 1 file changed, 2 insertions(+), 1 deletion(-)

artix in commit 95324b81:
 Support Redis Cluster Proxy PROXY INFO command
 1 file changed, 5 insertions(+), 1 deletion(-)

박승현 in commit 04c53fa1:
 Update redis.conf
 1 file changed, 1 insertion(+), 1 deletion(-)

WuYunlong in commit 0578157d:
 Fix master replica inconsistency for upgrading scenario.
 3 files changed, 9 insertions(+), 2 deletions(-)

WuYunlong in commit 299f1d02:
 Add 14-consistency-check.tcl to prove there is a data consistency issue.
 1 file changed, 87 insertions(+)

antirez in commit 61b98f32:
 Regression test for #7011.
 1 file changed, 7 insertions(+)

antirez in commit 34ea2f4e:
 ACL: default user off should not allow automatic authentication.
 2 files changed, 3 insertions(+), 2 deletions(-)

antirez in commit cbbf9b39:
 Sentinel: document auth-user directive.
 1 file changed, 12 insertions(+)

antirez in commit 9c2e42dd:
 ACL: Make Redis 6 more backward compatible with requirepass.
 4 files changed, 17 insertions(+), 15 deletions(-)

antirez in commit d387f67d:
 Sentinel: implement auth-user directive for ACLs.
 1 file changed, 38 insertions(+), 7 deletions(-)

zhaozhao.zz in commit 7c078416:
 Threaded IO: bugfix client kill may crash redis
 1 file changed, 11 insertions(+), 5 deletions(-)

zhaozhao.zz in commit 9cc7038e:
 Threaded IO: handle pending reads clients ASAP after event loop
 1 file changed, 3 insertions(+), 1 deletion(-)

antirez in commit da8c7c49:
 Example sentinel conf: document requirepass.
 1 file changed, 8 insertions(+)

antirez in commit bdb338cf:
 Aesthetic changes in PR #6989.
 1 file changed, 9 insertions(+), 5 deletions(-)

zhaozhao.zz in commit b3e03054:
 Threaded IO: bugfix #6988 process events while blocked
 1 file changed, 5 insertions(+)

antirez in commit e628f944:
 Restore newline at the end of redis-cli.c
 1 file changed, 2 insertions(+), 1 deletion(-)

chendianqiang in commit 5d4c4df3:
 use correct list for moduleUnregisterUsedAPI
 1 file changed, 1 insertion(+), 1 deletion(-)

guodongxiaren in commit da14982d:
 string literal should be const char*
 1 file changed, 1 insertion(+), 1 deletion(-)

Itamar Haber in commit dc8885a1:
 Adds keyspace notifications to migrate and restore
 1 file changed, 3 insertions(+), 1 deletion(-)

bodong.ybd in commit bfb18e55:
 Remove duplicate obj files in Makefile
 1 file changed, 2 insertions(+), 2 deletions(-)

bodong.ybd in commit 76d57161:
 Fix bug of tcl test using external server
 2 files changed, 8 insertions(+), 2 deletions(-)

fengpf in commit 0e5820d8:
 fix comments in latency.c
 2 files changed, 2 insertions(+), 1 deletion(-)

antirez in commit 916dd79f:
 Update linenoise.
 1 file changed, 2 insertions(+), 1 deletion(-)

lifubang in commit c0c67c9b:
 add askpass mode
 1 file changed, 19 insertions(+), 1 deletion(-)

lifubang in commit e1c29434:
 update linenoise to https://github.com/antirez/linenoise/tree/fc9667a81d43911a6690fb1e68c16e6e3bb8df05
 4 files changed, 59 insertions(+), 4 deletions(-)

Jamie Scott in commit e5a063bc:
 Remove default guidance in Redis.conf
 1 file changed, 1 insertion(+), 2 deletions(-)

Jamie Scott in commit d28cbaf7:
 Update Redis.conf to improve TLS usability
 1 file changed, 2 insertions(+), 1 deletion(-)

Johannes Truschnigg in commit 23d5e8b8:
 Signal systemd readiness atfer Partial Resync
 1 file changed, 4 insertions(+)

Oran Agra in commit 61738154:
 fix for flaky psync2 test
 1 file changed, 21 insertions(+)

antirez in commit 70e0e499:
 ae.c: fix crash when resizing the event loop.
 1 file changed, 6 insertions(+), 2 deletions(-)

antirez in commit b3e4aa67:
 Fix release notes spelling mistake.
 1 file changed, 1 insertion(+), 1 deletion(-)


================================================================================
Redis 6.0 RC2     Released Thu Mar 05 15:40:53 CET 2020
================================================================================

Upgrade urgency MODERATE: Normal bugfixing release of a non-GA branch.

Hi Redis users, Redis 6 is approaching and will be released 30th of April.
New release candidates will be released at the end of March, then another
one mid April, to finally reach the GA at the end of April.

Redis 6 RC2 brings many fixes and new things, especially in the area of
client side caching. This is the list of big changes in this release. As
usually you can find the full list of commits at the end:

New features and improvements:

* ACL LOG: log denied commands, keys accesses and authentications.
* Client side caching redesigned. Now we use keys not caching slots.
* Client side caching: Broadcasting mode implemented.
* Client side caching: OPTIN/OPTOUT modes implemented.
* Remove RDB files used for replication in persistence-less instances (option).

Fixes (only selected ones, see commits for all the fixes):

* Different fixes to streams in edge cases.
* Fix duplicated CLIENT SETNAME reply because of RESP3 changes.
* Fix crash due to new active expire division by zero.
* Avoid sentinel changes promoted_slave to be its own replica.
* Fix bug on KEYS command where pattern starts with * followed by \x00.
* Threaded I/O: now the main thread is used as well to do I/O.
* Many fixes to modules APIs, and more to come in the next RCs.
* ld2string should fail if string contains \0 in the middle.
* Make the Redis test more reliable.
* Fix SPOP returning nil (see #4709). WARNING: API change.

qetu3790 in commit 4af0d7fd:
 Fix not used constant in lru_test_mode.
 1 file changed, 1 insertion(+), 1 deletion(-)

hwware in commit 6ef01878:
 add missing file marco
 1 file changed, 5 insertions(+)

ShooterIT in commit fe81d5c8:
 Avoid compiler warnings
 1 file changed, 1 insertion(+)

antirez in commit c2f01d7f:
 RDB deletion: document it in example redis.conf.
 1 file changed, 13 insertions(+)

antirez in commit 127e09bc:
 Make sync RDB deletion configurable. Default to no.
 3 files changed, 22 insertions(+), 4 deletions(-)

antirez in commit a20303c6:
 Check that the file exists in removeRDBUsedToSyncReplicas().
 1 file changed, 8 insertions(+), 4 deletions(-)

antirez in commit 7a23b945:
 Log RDB deletion in persistence-less instances.
 1 file changed, 15 insertions(+), 2 deletions(-)

antirez in commit baaf869f:
 Introduce bg_unlink().
 1 file changed, 31 insertions(+), 3 deletions(-)

antirez in commit be4bc1a5:
 Remove RDB files used for replication in persistence-less instances.
 3 files changed, 56 insertions(+), 1 deletion(-)

antirez in commit 07dc1b42:
 Use a smaller getkeys global buffer.
 1 file changed, 1 insertion(+), 1 deletion(-)

Oran Agra in commit 10e71b3d:
 Optimize temporary memory allocations for getKeysFromCommand mechanism
 1 file changed, 31 insertions(+), 10 deletions(-)

antirez in commit edc0ed14:
 Modules: reformat RM_Scan() top comment a bit.
 1 file changed, 21 insertions(+), 12 deletions(-)

antirez in commit c5319612:
 Modules: more details in RM_Scan API top comment.
 1 file changed, 22 insertions(+), 6 deletions(-)

Oran Agra in commit fff6b26a:
 RM_Scan disable dict rehashing
 2 files changed, 21 insertions(+), 6 deletions(-)

Guy Benoish in commit 65048460:
 Add RM_CreateStringFromDouble
 2 files changed, 14 insertions(+)

Oran Agra in commit 3144a278:
 add no_auth to COMMAND INFO
 1 file changed, 1 insertion(+)

Oran Agra in commit afe0b16c:
 module api docs for aux_save and aux_load
 2 files changed, 7 insertions(+), 1 deletion(-)

Guy Benoish in commit df152b0c:
 streamReplyWithRangeFromConsumerPEL: Redundant streamDecodeID
 1 file changed, 1 insertion(+), 3 deletions(-)

antirez in commit e3c1f439:
 Show Redis version when not understanding a config directive.
 1 file changed, 2 insertions(+), 1 deletion(-)

antirez in commit 141c0679:
 Changelog: explain Redis 6 SPOP change.
 1 file changed, 4 insertions(+), 1 deletion(-)

bodong.ybd in commit fe902461:
 Fix spop return nil #4709
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 9d4219eb:
 Fix SDS misuse in enumConfigSet(). Related to #6778.
 1 file changed, 3 insertions(+), 3 deletions(-)

antirez in commit 84243064:
 Remove useless comment from enumConfigSet().
 1 file changed, 1 deletion(-)

Ponnuvel Palaniyappan in commit dafb94db:
 Fix a potential overflow with strncpy
 1 file changed, 5 insertions(+), 5 deletions(-)

antirez in commit ea697b63:
 Improve aeDeleteEventLoop() top comment grammar.
 1 file changed, 2 insertions(+), 1 deletion(-)

wangyuan21 in commit dd479880:
 free time event when delete eventloop
 1 file changed, 7 insertions(+)

srzhao in commit ecf3b2ef:
 fix impl of aof-child whitelist SIGUSR1 feature.
 1 file changed, 5 insertions(+), 4 deletions(-)

<EMAIL> in commit 2966132c:
 Changed log level for module fork api from 'notice' to 'verbos'.
 1 file changed, 2 insertions(+), 2 deletions(-)

hwware in commit 7277e5d8:
 format fix
 1 file changed, 1 insertion(+), 1 deletion(-)

hwware in commit 1bb5ee9c:
 fix potentical memory leaks
 1 file changed, 4 insertions(+), 1 deletion(-)

Hengjian Tang in commit 97329733:
 modify the read buf size according to the write buf size PROTO_IOBUF_LEN defined before
 1 file changed, 1 insertion(+), 1 deletion(-)

Ariel in commit 15ea1324:
 fix ThreadSafeContext lock/unlock function names
 1 file changed, 2 insertions(+), 2 deletions(-)

Guy Benoish in commit 4d12c37c:
 XREADGROUP should propagate XCALIM/SETID in MULTI/EXEC
 1 file changed, 2 insertions(+), 2 deletions(-)

Oran Agra in commit 12626ce9:
 fix race in module api test for fork
 2 files changed, 2 insertions(+), 3 deletions(-)

Guy Benoish in commit 2ecab0b6:
 Modules: Do not auto-unblock clients if not blocked on keys
 1 file changed, 22 insertions(+), 7 deletions(-)

Oran Agra in commit 635321d4:
 fix github actions failing latency test for active defrag - part 2
 2 files changed, 5 insertions(+), 4 deletions(-)

Oran Agra in commit 0b988fa9:
 fix github actions failing latency test for active defrag
 2 files changed, 14 insertions(+), 13 deletions(-)

Oran Agra in commit 60096bc1:
 Fix latency sensitivity of new defrag test
 1 file changed, 32 insertions(+), 8 deletions(-)

antirez in commit b4395426:
 Tracking: optin/out implemented.
 3 files changed, 82 insertions(+), 16 deletions(-)

antirez in commit ef3551d1:
 Test engine: experimental change to avoid busy port problems.
 1 file changed, 84 insertions(+), 49 deletions(-)

antirez in commit 72c05351:
 Test engine: detect timeout when checking for Redis startup.
 1 file changed, 11 insertions(+), 1 deletion(-)

antirez in commit 294c9af4:
 Test engine: better tracking of what workers are doing.
 2 files changed, 12 insertions(+), 4 deletions(-)

hwware in commit ba027079:
 add missing subcommand description for debug oom
 1 file changed, 1 insertion(+)

Guy Benoish in commit 5d0890c0:
 Fix memory leak in test_ld_conv
 1 file changed, 4 insertions(+)

Madelyn Olson in commit d1f22eac:
 Give an error message if you specify redirect twice
 1 file changed, 7 insertions(+)

Madelyn Olson in commit 762fbcb6:
 Minor CSC fixes and fixed documentation
 2 files changed, 16 insertions(+), 17 deletions(-)

Oran Agra in commit 349aa245:
 Defrag big lists in portions to avoid latency and freeze
 4 files changed, 350 insertions(+), 34 deletions(-)

Guy Benoish in commit b4ddc7b7:
 XGROUP DESTROY should unblock XREADGROUP with -NOGROUP
 2 files changed, 11 insertions(+)

hayashier in commit 73806f74:
 fix typo from fss to rss
 1 file changed, 2 insertions(+), 2 deletions(-)

antirez in commit b6129f86:
 Test is more complex now, increase default timeout.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit f15fb727:
 Tracking: fix max-keys configuration directive.
 2 files changed, 2 insertions(+), 2 deletions(-)

Itamar Haber in commit e374573f:
 Fixes segfault on calling trackingGetTotalKeys
 1 file changed, 1 insertion(+)

antirez in commit 73d47d57:
 Signal key as modified when expired on-access.
 1 file changed, 4 insertions(+), 2 deletions(-)

antirez in commit b7cb28d5:
 Tracking: first set of tests for the feature.
 1 file changed, 66 insertions(+)

antirez in commit 1db72571:
 Tracking: fix operators precedence error in bcast check.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit fe96e29d:
 Tracking: fix behavior when switchinig from normal to BCAST.
 1 file changed, 11 insertions(+), 1 deletion(-)

antirez in commit f21be1ec:
 Tracking: fix sending messages bug + tracking off bug.
 2 files changed, 28 insertions(+), 20 deletions(-)

antirez in commit 6fb1aa23:
 Tracking: BCAST: basic feature now works.
 3 files changed, 55 insertions(+), 40 deletions(-)

antirez in commit d4fe79a1:
 Tracking: BCAST: broadcasting of keys in prefixes implemented.
 2 files changed, 94 insertions(+), 9 deletions(-)

antirez in commit abb81c63:
 Tracking: BCAST: registration in the prefix table.
 3 files changed, 67 insertions(+), 20 deletions(-)

antirez in commit 77da9608:
 Tracking: BCAST: parsing of the options + skeleton.
 4 files changed, 73 insertions(+), 19 deletions(-)

antirez in commit 3e8c69a9:
 Tracking: always reply with an array of keys.
 2 files changed, 10 insertions(+), 3 deletions(-)

antirez in commit a788c373:
 Tracking: minor change of names and new INFO field.
 4 files changed, 11 insertions(+), 4 deletions(-)

antirez in commit df838927:
 Rax.c: populate data field after random walk.
 1 file changed, 1 insertion(+)

antirez in commit 0517da36:
 Tracking: rename INFO field with total items.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 3c16d6b3:
 Tracking: first conversion from hashing to key names.
 3 files changed, 84 insertions(+), 114 deletions(-)

Oran Agra in commit 3b4f1477:
 add no-slowlog option to RM_CreateCommand
 1 file changed, 3 insertions(+)

Khem Raj in commit 5e762d84:
 Mark extern definition of SDS_NOINIT in sds.h
 1 file changed, 1 insertion(+), 1 deletion(-)

lifubang in commit 54f5499a:
 correct help info for --user and --pass
 1 file changed, 2 insertions(+), 2 deletions(-)

Seunghoon Woo in commit 0c952b13:
 [FIX] revisit CVE-2015-8080 vulnerability
 1 file changed, 6 insertions(+), 4 deletions(-)

Guy Benoish in commit dd34f703:
 Diskless-load emptyDb-related fixes
 3 files changed, 44 insertions(+), 28 deletions(-)

lifubang in commit 5e042dbc:
 fix ssl flag check for redis-cli
 1 file changed, 10 insertions(+), 9 deletions(-)

Guy Benoish in commit dcbe8bfa:
 Exclude "keymiss" notification from NOTIFY_ALL
 5 files changed, 12 insertions(+), 7 deletions(-)

Oran Agra in commit 36caf2e4:
 update RM_SignalModifiedKey doc comment
 1 file changed, 2 insertions(+), 1 deletion(-)

Oran Agra in commit 3067352a:
 Add handling of short read of module id in rdb
 1 file changed, 4 insertions(+), 1 deletion(-)

Yossi Gottlieb in commit 9baaf858:
 TLS: Update documentation.
 2 files changed, 32 insertions(+), 31 deletions(-)

Oran Agra in commit 4440133e:
 A few non-data commands that should be allowed while loading or stale
 1 file changed, 8 insertions(+), 8 deletions(-)

Oran Agra in commit c9577941:
 Memory leak when bind config is provided twice
 1 file changed, 4 insertions(+)

Oran Agra in commit 1333a46b:
 fix maxmemory config warning
 1 file changed, 3 insertions(+), 2 deletions(-)

Oran Agra in commit 8e7282eb:
 Fix client flags to be int64 in module.c
 1 file changed, 3 insertions(+), 3 deletions(-)

Oran Agra in commit a678390e:
 moduleRDBLoadError, add key name, and use panic rather than exit
 1 file changed, 5 insertions(+), 4 deletions(-)

Oran Agra in commit 919fbf42:
 reduce repeated calls to use_diskless_load
 1 file changed, 3 insertions(+), 4 deletions(-)

Oran Agra in commit 22e45d46:
 freeClientAsync don't lock mutex if there's just one thread
 1 file changed, 6 insertions(+), 1 deletion(-)

Oran Agra in commit ba289244:
 move restartAOFAfterSYNC from replicaofCommand to replicationUnsetMaster
 1 file changed, 4 insertions(+), 3 deletions(-)

Oran Agra in commit f42ce57d:
 stopAppendOnly resets aof_rewrite_scheduled
 1 file changed, 1 insertion(+)

Oran Agra in commit df096bc9:
 add SAVE subcommand to ACL HELP and top comment
 1 file changed, 2 insertions(+)

Oran Agra in commit a55e5847:
 DEBUG HELP - add PROTOCOL
 1 file changed, 3 insertions(+), 2 deletions(-)

Guy Benoish in commit 5a6cfbf4:
 Some refactroing using getClientType instead of CLIENT_SLAVE
 2 files changed, 18 insertions(+), 26 deletions(-)

Guy Benoish in commit fae306b3:
 Fix small bugs related to replica and monitor ambiguity
 2 files changed, 8 insertions(+), 6 deletions(-)

Yossi Gottlieb in commit 73630966:
 TLS: Some redis.conf clarifications.
 1 file changed, 10 insertions(+), 11 deletions(-)

Oran Agra in commit 488e1947:
 config.c verbose error replies for CONFIG SET, like config file parsing
 1 file changed, 31 insertions(+), 97 deletions(-)

Oran Agra in commit c82ccf06:
 memoryGetKeys helper function so that ACL can limit access to keys for MEMORY command
 3 files changed, 18 insertions(+), 1 deletion(-)

antirez in commit 51c1a9f8:
 ACL LOG: make max log entries configurable.
 4 files changed, 19 insertions(+)

antirez in commit ea1e1b12:
 ACL LOG: test for AUTH reason.
 1 file changed, 9 insertions(+)

antirez in commit 7379c78a:
 ACL LOG: log failed auth attempts.
 5 files changed, 34 insertions(+), 12 deletions(-)

antirez in commit 9f6e84f6:
 ACL LOG: implement a few basic tests.
 1 file changed, 87 insertions(+)

antirez in commit 82790e51:
 ACL LOG: also log ACL errors in the scripting/MULTI ctx.
 2 files changed, 6 insertions(+), 2 deletions(-)

antirez in commit 943008eb:
 ACL LOG: implement LOG RESET.
 1 file changed, 6 insertions(+), 2 deletions(-)

antirez in commit e271a611:
 ACL LOG: group similar entries in a given time delta.
 1 file changed, 58 insertions(+), 3 deletions(-)

antirez in commit f1974d5d:
 ACL LOG: actually emit entries.
 3 files changed, 34 insertions(+), 5 deletions(-)

antirez in commit d9b153c9:
 ACL LOG: implement ACL LOG subcommadn skeleton.
 1 file changed, 37 insertions(+)

antirez in commit 577fc438:
 ACL LOG: data structures and initial functions.
 5 files changed, 54 insertions(+), 5 deletions(-)

Leo Murillo in commit f7a94526:
 Set ZSKIPLIST_MAXLEVEL to optimal value given 2^64 elements and p=0.25
 1 file changed, 1 insertion(+), 1 deletion(-)

WuYunlong in commit eecfa979:
 Fix lua related memory leak.
 1 file changed, 1 insertion(+)

WuYunlong in commit d2509811:
 Add tcl regression test in scripting.tcl to reproduce memory leak.
 1 file changed, 5 insertions(+)

Yossi Gottlieb in commit 29d4a150:
 TLS: Fix missing initialization in redis-cli.
 1 file changed, 9 insertions(+)

Oran Agra in commit ec0c61da:
 fix uninitialized info_cb var in module.c
 1 file changed, 1 insertion(+)

Guy Benoish in commit 6fe55c2f:
 ld2string should fail if string contains \0 in the middle
 5 files changed, 20 insertions(+), 11 deletions(-)

antirez in commit bbce3ba9:
 Add more info in the unblockClientFromModule() function.
 1 file changed, 7 insertions(+), 1 deletion(-)

Guy Benoish in commit 40295fb3:
 Modules: Fix blocked-client-related memory leak
 3 files changed, 51 insertions(+), 6 deletions(-)

antirez in commit 8e9d19bc:
 Change error message for #6775.
 1 file changed, 2 insertions(+), 2 deletions(-)

Vasyl Melnychuk in commit ba146d4c:
 Make error when submitting command in incorrect context more explicit
 1 file changed, 4 insertions(+), 1 deletion(-)

antirez in commit 721a39dd:
 Document I/O threads in redis.conf.
 1 file changed, 46 insertions(+)

antirez in commit 5be3a15a:
 Setting N I/O threads should mean N-1 additional + 1 main thread.
 1 file changed, 25 insertions(+), 22 deletions(-)

antirez in commit cbabf779:
 Simplify #6379 changes.
 2 files changed, 4 insertions(+), 9 deletions(-)

WuYunlong in commit 658749cc:
 Free allocated sds in pfdebugCommand() to avoid memory leak.
 1 file changed, 1 insertion(+)

WuYunlong in commit 47988c96:
 Fix potential memory leak of clusterLoadConfig().
 1 file changed, 20 insertions(+), 5 deletions(-)

WuYunlong in commit cc90f79b:
 Fix potential memory leak of rioWriteBulkStreamID().
 1 file changed, 4 insertions(+), 1 deletion(-)

antirez in commit ecd17e81:
 Jump to right label on AOF parsing error.
 1 file changed, 6 insertions(+), 4 deletions(-)

antirez in commit 1927932b:
 Port PR #6110 to new connection object code.
 1 file changed, 2 insertions(+), 2 deletions(-)

antirez in commit f2df5773:
 A few comments about main thread serving I/O as well.
 1 file changed, 7 insertions(+), 1 deletion(-)

zhaozhao.zz in commit b3ff8a4b:
 Threaded IO: use main thread to handle read work
 1 file changed, 8 insertions(+), 1 deletion(-)

zhaozhao.zz in commit b1f2c510:
 Threaded IO: use main thread to handle write work
 1 file changed, 10 insertions(+), 2 deletions(-)

ShooterIT in commit 7bbafc56:
 Rename rdb asynchronously
 1 file changed, 7 insertions(+)

Leo Murillo in commit c7f75266:
 Fix bug on KEYS command where pattern starts with * followed by \x00 (null char).
 1 file changed, 1 insertion(+), 1 deletion(-)

Jamie Scott in commit ed7ea13a:
 Update to directive in redis.conf (missing s)
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 3be77623:
 Free fakeclient argv on AOF error.
 1 file changed, 11 insertions(+), 3 deletions(-)

antirez in commit 15f6b748:
 Git ignore: ignore more files.
 1 file changed, 2 insertions(+)

Guy Benoish in commit 1b5bf40c:
 Blocking XREAD[GROUP] should always reply with valid data (or timeout)
 3 files changed, 44 insertions(+), 10 deletions(-)

John Sully in commit 954c20ed:
 Add support for incremental build with header files
 2 files changed, 6 insertions(+), 1 deletion(-)

WuYunlong in commit 11c3afd7:
 Fix petential cluster link error.
 1 file changed, 4 insertions(+)

Yossi Gottlieb in commit b752e83d:
 Add REDISMODULE_CTX_FLAGS_MULTI_DIRTY.
 2 files changed, 8 insertions(+)

hwware in commit e16eb874:
 typo fix in acl.c
 1 file changed, 2 insertions(+), 2 deletions(-)

Itamar Haber in commit 35ea9d23:
 Adjusts 'io_threads_num' max to 128
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 38729126:
 XCLAIM: Create the consumer only on successful claims.
 1 file changed, 4 insertions(+), 2 deletions(-)

yz1509 in commit b9a15303:
 avoid sentinel changes promoted_slave to be its own replica.
 1 file changed, 1 insertion(+), 1 deletion(-)

antirez in commit 5e7e5e6b:
 Fix active expire division by zero.
 1 file changed, 7 insertions(+), 4 deletions(-)

antirez in commit e61dde88:
 Fix duplicated CLIENT SETNAME reply.
 1 file changed, 1 deletion(-)

Guy Benoish in commit cddf1da2:
 Stream: Handle streamID-related edge cases
 4 files changed, 54 insertions(+), 4 deletions(-)

Oran Agra in commit 52ea44e5:
 config.c adjust config limits and mutable
 2 files changed, 7 insertions(+), 7 deletions(-)

antirez in commit 0f28ea16:
 Inline protocol: handle empty strings well.
 1 file changed, 2 insertions(+), 6 deletions(-)

antirez in commit 00e5fefe:
 Fix ip and missing mode in RM_GetClusterNodeInfo().
 1 file changed, 5 insertions(+), 2 deletions(-)

================================================================================
Redis 6.0 RC1   Released Thu Dec 19 09:58:24 CEST 2019
================================================================================

Upgrade urgency LOW: This is the first RC of Redis 6.

Introduction to the Redis 6 release
===================================

Redis 6 improves Redis in a number of key areas and is one of the largest
Redis releases in the history of the project, so here we'll list only
the biggest features in this release:

* The modules system now has a number of new APIs that allow module authors
  to make things otherwise not possible in the past. It is possible to
  store arbitrary module private data in RDB files, to hook on different
  server events, capture and rewrite commands executions, block clients on
  keys, and so forth. 
* The Redis active expire cycle was rewritten for much faster eviction of keys
  that are already expired. Now the effort is tunable.
* Redis now supports SSL on all channels.
* ACL support, you can define users that can run only certain commands and/or
  can only access only certain keys patterns.
* Redis now supports a new protocol called RESP3, which returns more
  semantical replies: new clients using this protocol can understand just
  from the reply what type to return to the calling program.
* There is server-side support for client-side caching of key values. This
  feature is still experimental and will get more changes during the next
  release candidates, but you can already test it and read about it here:
  https://redis.io/topics/client-side-caching
* Redis can now optionally use threads to handle I/O, allowing to serve
  2 times as much operations per second in a single instance when
  pipelining cannot be used.
* Diskless replication is now supported even on replicas: a replica is now
  able, under certain conditions the user can configure, to load the RDB
  in the first synchronization directly from the socket to the memory.
* Redis-benchmark now supports a Redis Cluster mode.
* SRANDMEMBER and similar commands have a better distribution.
* Redis-cli improvements.
* Systemd support rewritten.
* A Redis Cluster proxy was released here:
  https://github.com/artix75/redis-cluster-proxy
* A Disque module for Redis was released here:
  https://github.com/antirez/disque-module

Thanks to all the users and developers who made this release possible.
We'll follow up with more RC releases, until the code looks production ready
and we don't get reports of serious issues for a while.

A special thank you for the amount of work put into this release
(in decreasing number of commits, only listing contributors with more
than a single commit) by:

   685  antirez
    81  zhaozhao.zz
    76  Oran Agra
    51  artix
    28  Madelyn Olson
    27  Yossi Gottlieb
    15  David Carlier
    14  Guy Benoish
    14  Guy Korland
    13  Itamar Haber
     9  Angus Pearson
     8  WuYunlong
     8  yongman
     7  vattezhang
     7  Chris Lamb
     5  Dvir Volk
     5  <EMAIL>
     5  chendianqiang
     5  John Sully
     4  dejun.xdj
     4  Daniel Dai
     4  Johannes Truschnigg
     4  swilly22
     3  Bruce Merry
     3  filipecosta90
     3  youjiali1995
     2  James Rouzier
     2  Andrey Bugaevskiy
     2  Brad Solomon
     2  Hamid Alaei
     2  Michael Chaten
     2  Steve Webster
     2  Wander Hillen
     2  Weiliang Li
     2  Yuan Zhou
     2  charsyam
     2  hujie
     2  jem
     2  shenlongxing
     2  valentino
     2  zhudacai 00228490
     2  喜欢兰花山丘

Migrating from 5.0 to 6.0
=========================

Redis 6.0 is mostly a strict superset of 5.0, you should not have any problem
upgrading your application from 5.0 to 6.0. However this is a list of small
non-backward compatible changes introduced in the 6.0 release:

* The SPOP <count> command no longer returns null when the set key does not
  exist. Now it returns the empty set as it should and as happens when it is
  called with a 0 argument. This is technically a fix, however it changes the
  old behavior.

--------------------------------------------------------------------------------

Credits: For each release, a list of changes with the relative author is
provided. Where not specified the implementation and design is done by
Salvatore Sanfilippo. Thanks to Redis Labs for making all this possible.
Also many thanks to all the other contributors and the amazing community
we have.

Commit messages may contain additional credits.

Enjoy,
Salvatore
