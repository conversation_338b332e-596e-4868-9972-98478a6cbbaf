[Note: this is the Redis manifesto, for general information about
       installing and running Redis read the README file instead.]

Redis Manifesto
===============

1 - A DSL for Abstract Data Types. Redis is a DSL (Domain Specific Language)
    that manipulates abstract data types and implemented as a TCP daemon.
    Commands manipulate a key space where keys are binary-safe strings and
    values are different kinds of abstract data types. Every data type
    represents an abstract version of a fundamental data structure. For instance
    Redis Lists are an abstract representation of linked lists. In Redis, the
    essence of a data type isn't just the kind of operations that the data types
    support, but also the space and time complexity of the data type and the
    operations performed upon it.

2 - Memory storage is #1. The Redis data set, composed of defined key-value
    pairs, is primarily stored in the computer's memory. The amount of memory in
    all kinds of computers, including entry-level servers, is increasing
    significantly each year. Memory is fast, and allows Redis to have very
    predictable performance. Datasets composed of 10k or 40 millions keys will
    perform similarly. Complex data types like Redis Sorted Sets are easy to
    implement and manipulate in memory with good performance, making Redis very
    simple. Redis will continue to explore alternative options (where data can
    be optionally stored on disk, say) but the main goal of the project remains
    the development of an in-memory database.

3 - Fundamental data structures for a fundamental API. The Redis API is a direct
    consequence of fundamental data structures. APIs can often be arbitrary but
    not an API that resembles the nature of fundamental data structures. If we
    ever meet intelligent life forms from another part of the universe, they'll
    likely know, understand and recognize the same basic data structures we have
    in our computer science books. Redis will avoid intermediate layers in API,
    so that the complexity is obvious and more complex operations can be
    performed as the sum of the basic operations.

4 - We believe in code efficiency. Computers get faster and faster, yet we
    believe that abusing computing capabilities is not wise: the amount of
    operations you can do for a given amount of energy remains anyway a
    significant parameter: it allows to do more with less computers and, at
    the same time, having a smaller environmental impact. Similarly Redis is
    able to "scale down" to smaller devices. It is perfectly usable in a
    Raspberry Pi and other small ARM based computers. Faster code having
    just the layers of abstractions that are really needed will also result,
    often, in more predictable performances. We think likewise about memory
    usage, one of the fundamental goals of the Redis project is to
    incrementally build more and more memory efficient data structures, so that
    problems that were not approachable in RAM in the past will be perfectly
    fine to handle in the future.

5 - Code is like a poem; it's not just something we write to reach some
    practical result. Sometimes people that are far from the Redis philosophy
    suggest using other code written by other authors (frequently in other
    languages) in order to implement something Redis currently lacks. But to us
    this is like if Shakespeare decided to end Enrico IV using the Paradiso from
    the Divina Commedia. Is using any external code a bad idea? Not at all. Like
    in "One Thousand and One Nights" smaller self contained stories are embedded
    in a bigger story, we'll be happy to use beautiful self contained libraries
    when needed. At the same time, when writing the Redis story we're trying to
    write smaller stories that will fit in to other code.

6 - We're against complexity. We believe designing systems is a fight against
    complexity. We'll accept to fight the complexity when it's worthwhile but
    we'll try hard to recognize when a small feature is not worth 1000s of lines
    of code. Most of the time the best way to fight complexity is by not
    creating it at all. Complexity is also a form of lock-in: code that is
    very hard to understand cannot be modified by users in an independent way
    regardless of the license. One of the main Redis goals is to remain
    understandable, enough for a single programmer to have a clear idea of how
    it works in detail just reading the source code for a couple of weeks.

7 - Threading is not a silver bullet. Instead of making Redis threaded we
    believe on the idea of an efficient (mostly) single threaded Redis core.
    Multiple of such cores, that may run in the same computer or may run
    in multiple computers, are abstracted away as a single big system by
    higher order protocols and features: Redis Cluster and the upcoming
    Redis Proxy are our main goals. A shared nothing approach is not just
    much simpler (see the previous point in this document), is also optimal
    in NUMA systems. In the specific case of Redis it allows for each instance
    to have a more limited amount of data, making the Redis persist-by-fork
    approach more sounding. In the future we may explore parallelism only for
    I/O, which is the low hanging fruit: minimal complexity could provide an
    improved single process experience.

8 - Two levels of API. The Redis API has two levels: 1) a subset of the API fits
    naturally into a distributed version of Redis and 2) a more complex API that
    supports multi-key operations. Both are useful if used judiciously but
    there's no way to make the more complex multi-keys API distributed in an
    opaque way without violating our other principles. We don't want to provide
    the illusion of something that will work magically when actually it can't in
    all cases. Instead we'll provide commands to quickly migrate keys from one
    instance to another to perform multi-key operations and expose the
    trade-offs to the user.

9 - We optimize for joy. We believe writing code is a lot of hard work, and the
    only way it can be worth is by enjoying it. When there is no longer joy in
    writing code, the best thing to do is stop. To prevent this, we'll avoid
    taking paths that will make Redis less of a joy to develop.

10 - All the above points are put together in what we call opportunistic
     programming: trying to get the most for the user with minimal increases
     in complexity (hanging fruits). Solve 95% of the problem with 5% of the
     code when it is acceptable. Avoid a fixed schedule but follow the flow of
     user requests, inspiration, Redis internal readiness for certain features
     (sometimes many past changes reach a critical point making a previously
     complex feature very easy to obtain).
