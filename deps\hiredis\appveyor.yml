# Appveyor configuration file for CI build of hiredis on Windows (under Cygwin)
environment:
    matrix:
        - CYG_BASH: C:\cygwin64\bin\bash
          CC: gcc
        - CYG_BASH: C:\cygwin\bin\bash
          CC: gcc
          CFLAGS: -m32
          CXXFLAGS: -m32
          LDFLAGS: -m32

clone_depth: 1

# Attempt to ensure we don't try to convert line endings to Win32 CRLF as this will cause build to fail
init:
    - git config --global core.autocrlf input

# Install needed build dependencies
install:
    - '%CYG_BASH% -lc "cygcheck -dc cygwin"'

build_script:
    - 'echo building...'
    - '%CYG_BASH% -lc "cd $APPVEYOR_BUILD_FOLDER; exec 0</dev/null; mkdir build && cd build && cmake .. -G \"Unix Makefiles\" && make VERBOSE=1"'
