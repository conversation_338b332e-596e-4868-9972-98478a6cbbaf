version: '{build}'

environment:
  matrix:
  - MSYSTEM: MINGW64
    CPU: x86_64
    MSVC: amd64
  - MSYSTEM: MINGW32
    CPU: i686
    MSVC: x86
  - MSYSTEM: MINGW64
    CPU: x86_64
  - MSYSTEM: MINGW32
    CPU: i686
  - MSYSTEM: MINGW64
    CPU: x86_64
    MSVC: amd64
    CONFIG_FLAGS: --enable-debug
  - MSYSTEM: MINGW32
    CPU: i686
    MSVC: x86
    CONFIG_FLAGS: --enable-debug
  - MSYSTEM: MINGW64
    CPU: x86_64
    CONFIG_FLAGS: --enable-debug
  - MSYSTEM: MINGW32
    CPU: i686
    CONFIG_FLAGS: --enable-debug

install:
  - set PATH=c:\msys64\%MSYSTEM%\bin;c:\msys64\usr\bin;%PATH%
  - if defined MSVC call "c:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat" %MSVC%
  - if defined MSVC pacman --noconfirm -Rsc mingw-w64-%CPU%-gcc gcc
  - pacman --noconfirm -Suy mingw-w64-%CPU%-make

build_script:
  - bash -c "autoconf"
  - bash -c "./configure $CONFIG_FLAGS"
  - mingw32-make
  - file lib/jemalloc.dll
  - mingw32-make tests
  - mingw32-make -k check
