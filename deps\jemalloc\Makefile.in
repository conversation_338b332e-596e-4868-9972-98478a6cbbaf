# Clear out all vpaths, then set just one (default vpath) for the main build
# directory.
vpath
vpath % .

# Clear the default suffixes, so that built-in rules are not used.
.SUFFIXES :

SHELL := /bin/sh

CC := @CC@
CXX := @CXX@

# Configuration parameters.
DESTDIR =
BINDIR := $(DESTDIR)@BINDIR@
INCLUDEDIR := $(DESTDIR)@INCLUDEDIR@
LIBDIR := $(DESTDIR)@LIBDIR@
DATADIR := $(DESTDIR)@DATADIR@
MANDIR := $(DESTDIR)@MANDIR@
srcroot := @srcroot@
objroot := @objroot@
abs_srcroot := @abs_srcroot@
abs_objroot := @abs_objroot@

# Build parameters.
CPPFLAGS := @CPPFLAGS@ -I$(objroot)include -I$(srcroot)include
CONFIGURE_CFLAGS := @CONFIGURE_CFLAGS@
SPECIFIED_CFLAGS := @SPECIFIED_CFLAGS@
EXTRA_CFLAGS := @EXTRA_CFLAGS@
CFLAGS := $(strip $(CONFIGURE_CFLAGS) $(SPECIFIED_CFLAGS) $(EXTRA_CFLAGS))
CONFIGURE_CXXFLAGS := @CONFIGURE_CXXFLAGS@
SPECIFIED_CXXFLAGS := @SPECIFIED_CXXFLAGS@
EXTRA_CXXFLAGS := @EXTRA_CXXFLAGS@
CXXFLAGS := $(strip $(CONFIGURE_CXXFLAGS) $(SPECIFIED_CXXFLAGS) $(EXTRA_CXXFLAGS))
LDFLAGS := @LDFLAGS@
EXTRA_LDFLAGS := @EXTRA_LDFLAGS@
LIBS := @LIBS@
RPATH_EXTRA := @RPATH_EXTRA@
SO := @so@
IMPORTLIB := @importlib@
O := @o@
A := @a@
EXE := @exe@
LIBPREFIX := @libprefix@
REV := @rev@
install_suffix := @install_suffix@
ABI := @abi@
XSLTPROC := @XSLTPROC@
AUTOCONF := @AUTOCONF@
_RPATH = @RPATH@
RPATH = $(if $(1),$(call _RPATH,$(1)))
cfghdrs_in := $(addprefix $(srcroot),@cfghdrs_in@)
cfghdrs_out := @cfghdrs_out@
cfgoutputs_in := $(addprefix $(srcroot),@cfgoutputs_in@)
cfgoutputs_out := @cfgoutputs_out@
enable_autogen := @enable_autogen@
enable_prof := @enable_prof@
enable_zone_allocator := @enable_zone_allocator@
MALLOC_CONF := @JEMALLOC_CPREFIX@MALLOC_CONF
link_whole_archive := @link_whole_archive@
DSO_LDFLAGS = @DSO_LDFLAGS@
SOREV = @SOREV@
PIC_CFLAGS = @PIC_CFLAGS@
CTARGET = @CTARGET@
LDTARGET = @LDTARGET@
TEST_LD_MODE = @TEST_LD_MODE@
MKLIB = @MKLIB@
AR = @AR@
ARFLAGS = @ARFLAGS@
DUMP_SYMS = @DUMP_SYMS@
AWK := @AWK@
CC_MM = @CC_MM@
LM := @LM@
INSTALL = @INSTALL@

ifeq (macho, $(ABI))
TEST_LIBRARY_PATH := DYLD_FALLBACK_LIBRARY_PATH="$(objroot)lib"
else
ifeq (pecoff, $(ABI))
TEST_LIBRARY_PATH := PATH="$(PATH):$(objroot)lib"
else
TEST_LIBRARY_PATH :=
endif
endif

LIBJEMALLOC := $(LIBPREFIX)jemalloc$(install_suffix)

# Lists of files.
BINS := $(objroot)bin/jemalloc-config $(objroot)bin/jemalloc.sh $(objroot)bin/jeprof
C_HDRS := $(objroot)include/jemalloc/jemalloc$(install_suffix).h
C_SRCS := $(srcroot)src/jemalloc.c \
	$(srcroot)src/arena.c \
	$(srcroot)src/background_thread.c \
	$(srcroot)src/base.c \
	$(srcroot)src/bin.c \
	$(srcroot)src/bitmap.c \
	$(srcroot)src/ckh.c \
	$(srcroot)src/ctl.c \
	$(srcroot)src/div.c \
	$(srcroot)src/extent.c \
	$(srcroot)src/extent_dss.c \
	$(srcroot)src/extent_mmap.c \
	$(srcroot)src/hash.c \
	$(srcroot)src/hooks.c \
	$(srcroot)src/large.c \
	$(srcroot)src/log.c \
	$(srcroot)src/malloc_io.c \
	$(srcroot)src/mutex.c \
	$(srcroot)src/mutex_pool.c \
	$(srcroot)src/nstime.c \
	$(srcroot)src/pages.c \
	$(srcroot)src/prng.c \
	$(srcroot)src/prof.c \
	$(srcroot)src/rtree.c \
	$(srcroot)src/stats.c \
	$(srcroot)src/sz.c \
	$(srcroot)src/tcache.c \
	$(srcroot)src/ticker.c \
	$(srcroot)src/tsd.c \
	$(srcroot)src/witness.c
ifeq ($(enable_zone_allocator), 1)
C_SRCS += $(srcroot)src/zone.c
endif
ifeq ($(IMPORTLIB),$(SO))
STATIC_LIBS := $(objroot)lib/$(LIBJEMALLOC).$(A)
endif
ifdef PIC_CFLAGS
STATIC_LIBS += $(objroot)lib/$(LIBJEMALLOC)_pic.$(A)
else
STATIC_LIBS += $(objroot)lib/$(LIBJEMALLOC)_s.$(A)
endif
DSOS := $(objroot)lib/$(LIBJEMALLOC).$(SOREV)
ifneq ($(SOREV),$(SO))
DSOS += $(objroot)lib/$(LIBJEMALLOC).$(SO)
endif
ifeq (1, $(link_whole_archive))
LJEMALLOC := -Wl,--whole-archive -L$(objroot)lib -l$(LIBJEMALLOC) -Wl,--no-whole-archive
else
LJEMALLOC := $(objroot)lib/$(LIBJEMALLOC).$(IMPORTLIB)
endif
PC := $(objroot)jemalloc.pc
MAN3 := $(objroot)doc/jemalloc$(install_suffix).3
DOCS_XML := $(objroot)doc/jemalloc$(install_suffix).xml
DOCS_HTML := $(DOCS_XML:$(objroot)%.xml=$(objroot)%.html)
DOCS_MAN3 := $(DOCS_XML:$(objroot)%.xml=$(objroot)%.3)
DOCS := $(DOCS_HTML) $(DOCS_MAN3)
C_TESTLIB_SRCS := $(srcroot)test/src/btalloc.c $(srcroot)test/src/btalloc_0.c \
	$(srcroot)test/src/btalloc_1.c $(srcroot)test/src/math.c \
	$(srcroot)test/src/mtx.c $(srcroot)test/src/mq.c \
	$(srcroot)test/src/SFMT.c $(srcroot)test/src/test.c \
	$(srcroot)test/src/thd.c $(srcroot)test/src/timer.c
ifeq (1, $(link_whole_archive))
C_UTIL_INTEGRATION_SRCS :=
C_UTIL_CPP_SRCS :=
else
C_UTIL_INTEGRATION_SRCS := $(srcroot)src/nstime.c $(srcroot)src/malloc_io.c
C_UTIL_CPP_SRCS := $(srcroot)src/nstime.c $(srcroot)src/malloc_io.c
endif
TESTS_UNIT := \
	$(srcroot)test/unit/a0.c \
	$(srcroot)test/unit/arena_reset.c \
	$(srcroot)test/unit/atomic.c \
	$(srcroot)test/unit/background_thread.c \
	$(srcroot)test/unit/background_thread_enable.c \
	$(srcroot)test/unit/base.c \
	$(srcroot)test/unit/bitmap.c \
	$(srcroot)test/unit/ckh.c \
	$(srcroot)test/unit/decay.c \
	$(srcroot)test/unit/div.c \
	$(srcroot)test/unit/emitter.c \
	$(srcroot)test/unit/extent_quantize.c \
	$(srcroot)test/unit/fork.c \
	$(srcroot)test/unit/hash.c \
	$(srcroot)test/unit/hooks.c \
	$(srcroot)test/unit/junk.c \
	$(srcroot)test/unit/junk_alloc.c \
	$(srcroot)test/unit/junk_free.c \
	$(srcroot)test/unit/log.c \
	$(srcroot)test/unit/mallctl.c \
	$(srcroot)test/unit/malloc_io.c \
	$(srcroot)test/unit/math.c \
	$(srcroot)test/unit/mq.c \
	$(srcroot)test/unit/mtx.c \
	$(srcroot)test/unit/pack.c \
	$(srcroot)test/unit/pages.c \
	$(srcroot)test/unit/ph.c \
	$(srcroot)test/unit/prng.c \
	$(srcroot)test/unit/prof_accum.c \
	$(srcroot)test/unit/prof_active.c \
	$(srcroot)test/unit/prof_gdump.c \
	$(srcroot)test/unit/prof_idump.c \
	$(srcroot)test/unit/prof_reset.c \
	$(srcroot)test/unit/prof_tctx.c \
	$(srcroot)test/unit/prof_thread_name.c \
	$(srcroot)test/unit/ql.c \
	$(srcroot)test/unit/qr.c \
	$(srcroot)test/unit/rb.c \
	$(srcroot)test/unit/retained.c \
	$(srcroot)test/unit/rtree.c \
	$(srcroot)test/unit/SFMT.c \
	$(srcroot)test/unit/size_classes.c \
	$(srcroot)test/unit/slab.c \
	$(srcroot)test/unit/smoothstep.c \
	$(srcroot)test/unit/spin.c \
	$(srcroot)test/unit/stats.c \
	$(srcroot)test/unit/stats_print.c \
	$(srcroot)test/unit/ticker.c \
	$(srcroot)test/unit/nstime.c \
	$(srcroot)test/unit/tsd.c \
	$(srcroot)test/unit/witness.c \
	$(srcroot)test/unit/zero.c
ifeq (@enable_prof@, 1)
TESTS_UNIT += \
	$(srcroot)test/unit/arena_reset_prof.c
endif
TESTS_INTEGRATION := $(srcroot)test/integration/aligned_alloc.c \
	$(srcroot)test/integration/allocated.c \
	$(srcroot)test/integration/extent.c \
	$(srcroot)test/integration/mallocx.c \
	$(srcroot)test/integration/MALLOCX_ARENA.c \
	$(srcroot)test/integration/overflow.c \
	$(srcroot)test/integration/posix_memalign.c \
	$(srcroot)test/integration/rallocx.c \
	$(srcroot)test/integration/sdallocx.c \
	$(srcroot)test/integration/thread_arena.c \
	$(srcroot)test/integration/thread_tcache_enabled.c \
	$(srcroot)test/integration/xallocx.c
ifeq (@enable_cxx@, 1)
CPP_SRCS := $(srcroot)src/jemalloc_cpp.cpp
TESTS_INTEGRATION_CPP := $(srcroot)test/integration/cpp/basic.cpp
else
CPP_SRCS :=
TESTS_INTEGRATION_CPP :=
endif
TESTS_STRESS := $(srcroot)test/stress/microbench.c

TESTS := $(TESTS_UNIT) $(TESTS_INTEGRATION) $(TESTS_INTEGRATION_CPP) $(TESTS_STRESS)

PRIVATE_NAMESPACE_HDRS := $(objroot)include/jemalloc/internal/private_namespace.h $(objroot)include/jemalloc/internal/private_namespace_jet.h
PRIVATE_NAMESPACE_GEN_HDRS := $(PRIVATE_NAMESPACE_HDRS:%.h=%.gen.h)
C_SYM_OBJS := $(C_SRCS:$(srcroot)%.c=$(objroot)%.sym.$(O))
C_SYMS := $(C_SRCS:$(srcroot)%.c=$(objroot)%.sym)
C_OBJS := $(C_SRCS:$(srcroot)%.c=$(objroot)%.$(O))
CPP_OBJS := $(CPP_SRCS:$(srcroot)%.cpp=$(objroot)%.$(O))
C_PIC_OBJS := $(C_SRCS:$(srcroot)%.c=$(objroot)%.pic.$(O))
CPP_PIC_OBJS := $(CPP_SRCS:$(srcroot)%.cpp=$(objroot)%.pic.$(O))
C_JET_SYM_OBJS := $(C_SRCS:$(srcroot)%.c=$(objroot)%.jet.sym.$(O))
C_JET_SYMS := $(C_SRCS:$(srcroot)%.c=$(objroot)%.jet.sym)
C_JET_OBJS := $(C_SRCS:$(srcroot)%.c=$(objroot)%.jet.$(O))
C_TESTLIB_UNIT_OBJS := $(C_TESTLIB_SRCS:$(srcroot)%.c=$(objroot)%.unit.$(O))
C_TESTLIB_INTEGRATION_OBJS := $(C_TESTLIB_SRCS:$(srcroot)%.c=$(objroot)%.integration.$(O))
C_UTIL_INTEGRATION_OBJS := $(C_UTIL_INTEGRATION_SRCS:$(srcroot)%.c=$(objroot)%.integration.$(O))
C_TESTLIB_STRESS_OBJS := $(C_TESTLIB_SRCS:$(srcroot)%.c=$(objroot)%.stress.$(O))
C_TESTLIB_OBJS := $(C_TESTLIB_UNIT_OBJS) $(C_TESTLIB_INTEGRATION_OBJS) $(C_UTIL_INTEGRATION_OBJS) $(C_TESTLIB_STRESS_OBJS)

TESTS_UNIT_OBJS := $(TESTS_UNIT:$(srcroot)%.c=$(objroot)%.$(O))
TESTS_INTEGRATION_OBJS := $(TESTS_INTEGRATION:$(srcroot)%.c=$(objroot)%.$(O))
TESTS_INTEGRATION_CPP_OBJS := $(TESTS_INTEGRATION_CPP:$(srcroot)%.cpp=$(objroot)%.$(O))
TESTS_STRESS_OBJS := $(TESTS_STRESS:$(srcroot)%.c=$(objroot)%.$(O))
TESTS_OBJS := $(TESTS_UNIT_OBJS) $(TESTS_INTEGRATION_OBJS) $(TESTS_STRESS_OBJS)
TESTS_CPP_OBJS := $(TESTS_INTEGRATION_CPP_OBJS)

.PHONY: all dist build_doc_html build_doc_man build_doc
.PHONY: install_bin install_include install_lib
.PHONY: install_doc_html install_doc_man install_doc install
.PHONY: tests check clean distclean relclean

.SECONDARY : $(PRIVATE_NAMESPACE_GEN_HDRS) $(TESTS_OBJS) $(TESTS_CPP_OBJS)

# Default target.
all: build_lib

dist: build_doc

$(objroot)doc/%.html : $(objroot)doc/%.xml $(srcroot)doc/stylesheet.xsl $(objroot)doc/html.xsl
	$(XSLTPROC) -o $@ $(objroot)doc/html.xsl $<

$(objroot)doc/%.3 : $(objroot)doc/%.xml $(srcroot)doc/stylesheet.xsl $(objroot)doc/manpages.xsl
	$(XSLTPROC) -o $@ $(objroot)doc/manpages.xsl $<

build_doc_html: $(DOCS_HTML)
build_doc_man: $(DOCS_MAN3)
build_doc: $(DOCS)

#
# Include generated dependency files.
#
ifdef CC_MM
-include $(C_SYM_OBJS:%.$(O)=%.d)
-include $(C_OBJS:%.$(O)=%.d)
-include $(CPP_OBJS:%.$(O)=%.d)
-include $(C_PIC_OBJS:%.$(O)=%.d)
-include $(CPP_PIC_OBJS:%.$(O)=%.d)
-include $(C_JET_SYM_OBJS:%.$(O)=%.d)
-include $(C_JET_OBJS:%.$(O)=%.d)
-include $(C_TESTLIB_OBJS:%.$(O)=%.d)
-include $(TESTS_OBJS:%.$(O)=%.d)
-include $(TESTS_CPP_OBJS:%.$(O)=%.d)
endif

$(C_SYM_OBJS): $(objroot)src/%.sym.$(O): $(srcroot)src/%.c
$(C_SYM_OBJS): CPPFLAGS += -DJEMALLOC_NO_PRIVATE_NAMESPACE
$(C_SYMS): $(objroot)src/%.sym: $(objroot)src/%.sym.$(O)
$(C_OBJS): $(objroot)src/%.$(O): $(srcroot)src/%.c
$(CPP_OBJS): $(objroot)src/%.$(O): $(srcroot)src/%.cpp
$(C_PIC_OBJS): $(objroot)src/%.pic.$(O): $(srcroot)src/%.c
$(C_PIC_OBJS): CFLAGS += $(PIC_CFLAGS)
$(CPP_PIC_OBJS): $(objroot)src/%.pic.$(O): $(srcroot)src/%.cpp
$(CPP_PIC_OBJS): CXXFLAGS += $(PIC_CFLAGS)
$(C_JET_SYM_OBJS): $(objroot)src/%.jet.sym.$(O): $(srcroot)src/%.c
$(C_JET_SYM_OBJS): CPPFLAGS += -DJEMALLOC_JET -DJEMALLOC_NO_PRIVATE_NAMESPACE
$(C_JET_SYMS): $(objroot)src/%.jet.sym: $(objroot)src/%.jet.sym.$(O)
$(C_JET_OBJS): $(objroot)src/%.jet.$(O): $(srcroot)src/%.c
$(C_JET_OBJS): CPPFLAGS += -DJEMALLOC_JET
$(C_TESTLIB_UNIT_OBJS): $(objroot)test/src/%.unit.$(O): $(srcroot)test/src/%.c
$(C_TESTLIB_UNIT_OBJS): CPPFLAGS += -DJEMALLOC_UNIT_TEST
$(C_TESTLIB_INTEGRATION_OBJS): $(objroot)test/src/%.integration.$(O): $(srcroot)test/src/%.c
$(C_TESTLIB_INTEGRATION_OBJS): CPPFLAGS += -DJEMALLOC_INTEGRATION_TEST
$(C_UTIL_INTEGRATION_OBJS): $(objroot)src/%.integration.$(O): $(srcroot)src/%.c
$(C_TESTLIB_STRESS_OBJS): $(objroot)test/src/%.stress.$(O): $(srcroot)test/src/%.c
$(C_TESTLIB_STRESS_OBJS): CPPFLAGS += -DJEMALLOC_STRESS_TEST -DJEMALLOC_STRESS_TESTLIB
$(C_TESTLIB_OBJS): CPPFLAGS += -I$(srcroot)test/include -I$(objroot)test/include
$(TESTS_UNIT_OBJS): CPPFLAGS += -DJEMALLOC_UNIT_TEST
$(TESTS_INTEGRATION_OBJS): CPPFLAGS += -DJEMALLOC_INTEGRATION_TEST
$(TESTS_INTEGRATION_CPP_OBJS): CPPFLAGS += -DJEMALLOC_INTEGRATION_CPP_TEST
$(TESTS_STRESS_OBJS): CPPFLAGS += -DJEMALLOC_STRESS_TEST
$(TESTS_OBJS): $(objroot)test/%.$(O): $(srcroot)test/%.c
$(TESTS_CPP_OBJS): $(objroot)test/%.$(O): $(srcroot)test/%.cpp
$(TESTS_OBJS): CPPFLAGS += -I$(srcroot)test/include -I$(objroot)test/include
$(TESTS_CPP_OBJS): CPPFLAGS += -I$(srcroot)test/include -I$(objroot)test/include
ifneq ($(IMPORTLIB),$(SO))
$(CPP_OBJS) $(C_SYM_OBJS) $(C_OBJS) $(C_JET_SYM_OBJS) $(C_JET_OBJS): CPPFLAGS += -DDLLEXPORT
endif

# Dependencies.
ifndef CC_MM
HEADER_DIRS = $(srcroot)include/jemalloc/internal \
	$(objroot)include/jemalloc $(objroot)include/jemalloc/internal
HEADERS = $(filter-out $(PRIVATE_NAMESPACE_HDRS),$(wildcard $(foreach dir,$(HEADER_DIRS),$(dir)/*.h)))
$(C_SYM_OBJS) $(C_OBJS) $(CPP_OBJS) $(C_PIC_OBJS) $(CPP_PIC_OBJS) $(C_JET_SYM_OBJS) $(C_JET_OBJS) $(C_TESTLIB_OBJS) $(TESTS_OBJS) $(TESTS_CPP_OBJS): $(HEADERS)
$(TESTS_OBJS) $(TESTS_CPP_OBJS): $(objroot)test/include/test/jemalloc_test.h
endif

$(C_OBJS) $(CPP_OBJS) $(C_PIC_OBJS) $(CPP_PIC_OBJS) $(C_TESTLIB_INTEGRATION_OBJS) $(C_UTIL_INTEGRATION_OBJS) $(TESTS_INTEGRATION_OBJS) $(TESTS_INTEGRATION_CPP_OBJS): $(objroot)include/jemalloc/internal/private_namespace.h
$(C_JET_OBJS) $(C_TESTLIB_UNIT_OBJS) $(C_TESTLIB_STRESS_OBJS) $(TESTS_UNIT_OBJS) $(TESTS_STRESS_OBJS): $(objroot)include/jemalloc/internal/private_namespace_jet.h

$(C_SYM_OBJS) $(C_OBJS) $(C_PIC_OBJS) $(C_JET_SYM_OBJS) $(C_JET_OBJS) $(C_TESTLIB_OBJS) $(TESTS_OBJS): %.$(O):
	@mkdir -p $(@D)
	$(CC) $(CFLAGS) -c $(CPPFLAGS) $(CTARGET) $<
ifdef CC_MM
	@$(CC) -MM $(CPPFLAGS) -MT $@ -o $(@:%.$(O)=%.d) $<
endif

$(C_SYMS): %.sym:
	@mkdir -p $(@D)
	$(DUMP_SYMS) $< | $(AWK) -f $(objroot)include/jemalloc/internal/private_symbols.awk > $@

$(C_JET_SYMS): %.sym:
	@mkdir -p $(@D)
	$(DUMP_SYMS) $< | $(AWK) -f $(objroot)include/jemalloc/internal/private_symbols_jet.awk > $@

$(objroot)include/jemalloc/internal/private_namespace.gen.h: $(C_SYMS)
	$(SHELL) $(srcroot)include/jemalloc/internal/private_namespace.sh $^ > $@

$(objroot)include/jemalloc/internal/private_namespace_jet.gen.h: $(C_JET_SYMS)
	$(SHELL) $(srcroot)include/jemalloc/internal/private_namespace.sh $^ > $@

%.h: %.gen.h
	@if ! `cmp -s $< $@` ; then echo "cp $< $<"; cp $< $@ ; fi

$(CPP_OBJS) $(CPP_PIC_OBJS) $(TESTS_CPP_OBJS): %.$(O):
	@mkdir -p $(@D)
	$(CXX) $(CXXFLAGS) -c $(CPPFLAGS) $(CTARGET) $<
ifdef CC_MM
	@$(CXX) -MM $(CPPFLAGS) -MT $@ -o $(@:%.$(O)=%.d) $<
endif

ifneq ($(SOREV),$(SO))
%.$(SO) : %.$(SOREV)
	@mkdir -p $(@D)
	ln -sf $(<F) $@
endif

$(objroot)lib/$(LIBJEMALLOC).$(SOREV) : $(if $(PIC_CFLAGS),$(C_PIC_OBJS),$(C_OBJS)) $(if $(PIC_CFLAGS),$(CPP_PIC_OBJS),$(CPP_OBJS))
	@mkdir -p $(@D)
	$(CC) $(DSO_LDFLAGS) $(call RPATH,$(RPATH_EXTRA)) $(LDTARGET) $+ $(LDFLAGS) $(LIBS) $(EXTRA_LDFLAGS)

$(objroot)lib/$(LIBJEMALLOC)_pic.$(A) : $(C_PIC_OBJS) $(CPP_PIC_OBJS)
$(objroot)lib/$(LIBJEMALLOC).$(A) : $(C_OBJS) $(CPP_OBJS)
$(objroot)lib/$(LIBJEMALLOC)_s.$(A) : $(C_OBJS) $(CPP_OBJS)

$(STATIC_LIBS):
	@mkdir -p $(@D)
	$(AR) $(ARFLAGS)@AROUT@ $+

$(objroot)test/unit/%$(EXE): $(objroot)test/unit/%.$(O) $(C_JET_OBJS) $(C_TESTLIB_UNIT_OBJS)
	@mkdir -p $(@D)
	$(CC) $(LDTARGET) $(filter %.$(O),$^) $(call RPATH,$(objroot)lib) $(LDFLAGS) $(filter-out -lm,$(LIBS)) $(LM) $(EXTRA_LDFLAGS)

$(objroot)test/integration/%$(EXE): $(objroot)test/integration/%.$(O) $(C_TESTLIB_INTEGRATION_OBJS) $(C_UTIL_INTEGRATION_OBJS) $(objroot)lib/$(LIBJEMALLOC).$(IMPORTLIB)
	@mkdir -p $(@D)
	$(CC) $(TEST_LD_MODE) $(LDTARGET) $(filter %.$(O),$^) $(call RPATH,$(objroot)lib) $(LJEMALLOC) $(LDFLAGS) $(filter-out -lm,$(filter -lrt -lpthread -lstdc++,$(LIBS))) $(LM) $(EXTRA_LDFLAGS)

$(objroot)test/integration/cpp/%$(EXE): $(objroot)test/integration/cpp/%.$(O) $(C_TESTLIB_INTEGRATION_OBJS) $(C_UTIL_INTEGRATION_OBJS) $(objroot)lib/$(LIBJEMALLOC).$(IMPORTLIB)
	@mkdir -p $(@D)
	$(CXX) $(LDTARGET) $(filter %.$(O),$^) $(call RPATH,$(objroot)lib) $(objroot)lib/$(LIBJEMALLOC).$(IMPORTLIB) $(LDFLAGS) $(filter-out -lm,$(LIBS)) -lm $(EXTRA_LDFLAGS)

$(objroot)test/stress/%$(EXE): $(objroot)test/stress/%.$(O) $(C_JET_OBJS) $(C_TESTLIB_STRESS_OBJS) $(objroot)lib/$(LIBJEMALLOC).$(IMPORTLIB)
	@mkdir -p $(@D)
	$(CC) $(TEST_LD_MODE) $(LDTARGET) $(filter %.$(O),$^) $(call RPATH,$(objroot)lib) $(objroot)lib/$(LIBJEMALLOC).$(IMPORTLIB) $(LDFLAGS) $(filter-out -lm,$(LIBS)) $(LM) $(EXTRA_LDFLAGS)

build_lib_shared: $(DSOS)
build_lib_static: $(STATIC_LIBS)
build_lib: build_lib_shared build_lib_static

install_bin:
	$(INSTALL) -d $(BINDIR)
	@for b in $(BINS); do \
	echo "$(INSTALL) -m 755 $$b $(BINDIR)"; \
	$(INSTALL) -m 755 $$b $(BINDIR); \
done

install_include:
	$(INSTALL) -d $(INCLUDEDIR)/jemalloc
	@for h in $(C_HDRS); do \
	echo "$(INSTALL) -m 644 $$h $(INCLUDEDIR)/jemalloc"; \
	$(INSTALL) -m 644 $$h $(INCLUDEDIR)/jemalloc; \
done

install_lib_shared: $(DSOS)
	$(INSTALL) -d $(LIBDIR)
	$(INSTALL) -m 755 $(objroot)lib/$(LIBJEMALLOC).$(SOREV) $(LIBDIR)
ifneq ($(SOREV),$(SO))
	ln -sf $(LIBJEMALLOC).$(SOREV) $(LIBDIR)/$(LIBJEMALLOC).$(SO)
endif

install_lib_static: $(STATIC_LIBS)
	$(INSTALL) -d $(LIBDIR)
	@for l in $(STATIC_LIBS); do \
	echo "$(INSTALL) -m 755 $$l $(LIBDIR)"; \
	$(INSTALL) -m 755 $$l $(LIBDIR); \
done

install_lib_pc: $(PC)
	$(INSTALL) -d $(LIBDIR)/pkgconfig
	@for l in $(PC); do \
	echo "$(INSTALL) -m 644 $$l $(LIBDIR)/pkgconfig"; \
	$(INSTALL) -m 644 $$l $(LIBDIR)/pkgconfig; \
done

install_lib: install_lib_shared install_lib_static install_lib_pc

install_doc_html:
	$(INSTALL) -d $(DATADIR)/doc/jemalloc$(install_suffix)
	@for d in $(DOCS_HTML); do \
	echo "$(INSTALL) -m 644 $$d $(DATADIR)/doc/jemalloc$(install_suffix)"; \
	$(INSTALL) -m 644 $$d $(DATADIR)/doc/jemalloc$(install_suffix); \
done

install_doc_man:
	$(INSTALL) -d $(MANDIR)/man3
	@for d in $(DOCS_MAN3); do \
	echo "$(INSTALL) -m 644 $$d $(MANDIR)/man3"; \
	$(INSTALL) -m 644 $$d $(MANDIR)/man3; \
done

install_doc: install_doc_html install_doc_man

install: install_bin install_include install_lib install_doc

tests_unit: $(TESTS_UNIT:$(srcroot)%.c=$(objroot)%$(EXE))
tests_integration: $(TESTS_INTEGRATION:$(srcroot)%.c=$(objroot)%$(EXE)) $(TESTS_INTEGRATION_CPP:$(srcroot)%.cpp=$(objroot)%$(EXE))
tests_stress: $(TESTS_STRESS:$(srcroot)%.c=$(objroot)%$(EXE))
tests: tests_unit tests_integration tests_stress

check_unit_dir:
	@mkdir -p $(objroot)test/unit
check_integration_dir:
	@mkdir -p $(objroot)test/integration
stress_dir:
	@mkdir -p $(objroot)test/stress
check_dir: check_unit_dir check_integration_dir

check_unit: tests_unit check_unit_dir
	$(SHELL) $(objroot)test/test.sh $(TESTS_UNIT:$(srcroot)%.c=$(objroot)%)
check_integration_prof: tests_integration check_integration_dir
ifeq ($(enable_prof), 1)
	$(MALLOC_CONF)="prof:true" $(SHELL) $(objroot)test/test.sh $(TESTS_INTEGRATION:$(srcroot)%.c=$(objroot)%) $(TESTS_INTEGRATION_CPP:$(srcroot)%.cpp=$(objroot)%)
	$(MALLOC_CONF)="prof:true,prof_active:false" $(SHELL) $(objroot)test/test.sh $(TESTS_INTEGRATION:$(srcroot)%.c=$(objroot)%) $(TESTS_INTEGRATION_CPP:$(srcroot)%.cpp=$(objroot)%)
endif
check_integration_decay: tests_integration check_integration_dir
	$(MALLOC_CONF)="dirty_decay_ms:-1,muzzy_decay_ms:-1" $(SHELL) $(objroot)test/test.sh $(TESTS_INTEGRATION:$(srcroot)%.c=$(objroot)%) $(TESTS_INTEGRATION_CPP:$(srcroot)%.cpp=$(objroot)%)
	$(MALLOC_CONF)="dirty_decay_ms:0,muzzy_decay_ms:0" $(SHELL) $(objroot)test/test.sh $(TESTS_INTEGRATION:$(srcroot)%.c=$(objroot)%) $(TESTS_INTEGRATION_CPP:$(srcroot)%.cpp=$(objroot)%)
check_integration: tests_integration check_integration_dir
	$(SHELL) $(objroot)test/test.sh $(TESTS_INTEGRATION:$(srcroot)%.c=$(objroot)%) $(TESTS_INTEGRATION_CPP:$(srcroot)%.cpp=$(objroot)%)
stress: tests_stress stress_dir
	$(SHELL) $(objroot)test/test.sh $(TESTS_STRESS:$(srcroot)%.c=$(objroot)%)
check: check_unit check_integration check_integration_decay check_integration_prof

clean:
	rm -f $(PRIVATE_NAMESPACE_HDRS)
	rm -f $(PRIVATE_NAMESPACE_GEN_HDRS)
	rm -f $(C_SYM_OBJS)
	rm -f $(C_SYMS)
	rm -f $(C_OBJS)
	rm -f $(CPP_OBJS)
	rm -f $(C_PIC_OBJS)
	rm -f $(CPP_PIC_OBJS)
	rm -f $(C_JET_SYM_OBJS)
	rm -f $(C_JET_SYMS)
	rm -f $(C_JET_OBJS)
	rm -f $(C_TESTLIB_OBJS)
	rm -f $(C_SYM_OBJS:%.$(O)=%.d)
	rm -f $(C_OBJS:%.$(O)=%.d)
	rm -f $(CPP_OBJS:%.$(O)=%.d)
	rm -f $(C_PIC_OBJS:%.$(O)=%.d)
	rm -f $(CPP_PIC_OBJS:%.$(O)=%.d)
	rm -f $(C_JET_SYM_OBJS:%.$(O)=%.d)
	rm -f $(C_JET_OBJS:%.$(O)=%.d)
	rm -f $(C_TESTLIB_OBJS:%.$(O)=%.d)
	rm -f $(TESTS_OBJS:%.$(O)=%$(EXE))
	rm -f $(TESTS_OBJS)
	rm -f $(TESTS_OBJS:%.$(O)=%.d)
	rm -f $(TESTS_OBJS:%.$(O)=%.out)
	rm -f $(TESTS_CPP_OBJS:%.$(O)=%$(EXE))
	rm -f $(TESTS_CPP_OBJS)
	rm -f $(TESTS_CPP_OBJS:%.$(O)=%.d)
	rm -f $(TESTS_CPP_OBJS:%.$(O)=%.out)
	rm -f $(DSOS) $(STATIC_LIBS)

distclean: clean
	rm -f $(objroot)bin/jemalloc-config
	rm -f $(objroot)bin/jemalloc.sh
	rm -f $(objroot)bin/jeprof
	rm -f $(objroot)config.log
	rm -f $(objroot)config.status
	rm -f $(objroot)config.stamp
	rm -f $(cfghdrs_out)
	rm -f $(cfgoutputs_out)

relclean: distclean
	rm -f $(objroot)configure
	rm -f $(objroot)VERSION
	rm -f $(DOCS_HTML)
	rm -f $(DOCS_MAN3)

#===============================================================================
# Re-configuration rules.

ifeq ($(enable_autogen), 1)
$(srcroot)configure : $(srcroot)configure.ac
	cd ./$(srcroot) && $(AUTOCONF)

$(objroot)config.status : $(srcroot)configure
	./$(objroot)config.status --recheck

$(srcroot)config.stamp.in : $(srcroot)configure.ac
	echo stamp > $(srcroot)config.stamp.in

$(objroot)config.stamp : $(cfgoutputs_in) $(cfghdrs_in) $(srcroot)configure
	./$(objroot)config.status
	@touch $@

# There must be some action in order for make to re-read Makefile when it is
# out of date.
$(cfgoutputs_out) $(cfghdrs_out) : $(objroot)config.stamp
	@true
endif
