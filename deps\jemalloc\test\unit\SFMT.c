/*
 * This file derives from SFMT 1.3.3
 * (http://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/SFMT/index.html), which was
 * released under the terms of the following license:
 *
 *   Copyright (c) 2006,2007 <PERSON><PERSON><PERSON>, <PERSON><PERSON> and Hiroshima
 *   University. All rights reserved.
 *
 *   Redistribution and use in source and binary forms, with or without
 *   modification, are permitted provided that the following conditions are
 *   met:
 *
 *       * Redistributions of source code must retain the above copyright
 *         notice, this list of conditions and the following disclaimer.
 *       * Redistributions in binary form must reproduce the above
 *         copyright notice, this list of conditions and the following
 *         disclaimer in the documentation and/or other materials provided
 *         with the distribution.
 *       * Neither the name of the Hiroshima University nor the names of
 *         its contributors may be used to endorse or promote products
 *         derived from this software without specific prior written
 *         permission.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *   "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *   LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *   A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *   OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *   SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *   LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *   DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *   THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *   (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *   OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
#include "test/jemalloc_test.h"

#define BLOCK_SIZE 10000
#define BLOCK_SIZE64 (BLOCK_SIZE / 2)
#define COUNT_1 1000
#define COUNT_2 700

static const uint32_t init_gen_rand_32_expected[] = {
	3440181298U, 1564997079U, 1510669302U, 2930277156U, 1452439940U,
	3796268453U,  423124208U, 2143818589U, 3827219408U, 2987036003U,
	2674978610U, 1536842514U, 2027035537U, 2534897563U, 1686527725U,
	 545368292U, 1489013321U, 1370534252U, 4231012796U, 3994803019U,
	1764869045U,  824597505U,  862581900U, 2469764249U,  812862514U,
	 359318673U,  116957936U, 3367389672U, 2327178354U, 1898245200U,
	3206507879U, 2378925033U, 1040214787U, 2524778605U, 3088428700U,
	1417665896U,  964324147U, 2282797708U, 2456269299U,  313400376U,
	2245093271U, 1015729427U, 2694465011U, 3246975184U, 1992793635U,
	 463679346U, 3721104591U, 3475064196U,  856141236U, 1499559719U,
	3522818941U, 3721533109U, 1954826617U, 1282044024U, 1543279136U,
	1301863085U, 2669145051U, 4221477354U, 3896016841U, 3392740262U,
	 462466863U, 1037679449U, 1228140306U,  922298197U, 1205109853U,
	1872938061U, 3102547608U, 2742766808U, 1888626088U, 4028039414U,
	 157593879U, 1136901695U, 4038377686U, 3572517236U, 4231706728U,
	2997311961U, 1189931652U, 3981543765U, 2826166703U,   87159245U,
	1721379072U, 3897926942U, 1790395498U, 2569178939U, 1047368729U,
	2340259131U, 3144212906U, 2301169789U, 2442885464U, 3034046771U,
	3667880593U, 3935928400U, 2372805237U, 1666397115U, 2460584504U,
	 513866770U, 3810869743U, 2147400037U, 2792078025U, 2941761810U,
	3212265810U,  984692259U,  346590253U, 1804179199U, 3298543443U,
	 750108141U, 2880257022U,  243310542U, 1869036465U, 1588062513U,
	2983949551U, 1931450364U, 4034505847U, 2735030199U, 1628461061U,
	2539522841U,  127965585U, 3992448871U,  913388237U,  559130076U,
	1202933193U, 4087643167U, 2590021067U, 2256240196U, 1746697293U,
	1013913783U, 1155864921U, 2715773730U,  915061862U, 1948766573U,
	2322882854U, 3761119102U, 1343405684U, 3078711943U, 3067431651U,
	3245156316U, 3588354584U, 3484623306U, 3899621563U, 4156689741U,
	3237090058U, 3880063844U,  862416318U, 4039923869U, 2303788317U,
	3073590536U,  701653667U, 2131530884U, 3169309950U, 2028486980U,
	 747196777U, 3620218225U,  432016035U, 1449580595U, 2772266392U,
	 444224948U, 1662832057U, 3184055582U, 3028331792U, 1861686254U,
	1104864179U,  342430307U, 1350510923U, 3024656237U, 1028417492U,
	2870772950U,  290847558U, 3675663500U,  508431529U, 4264340390U,
	2263569913U, 1669302976U,  519511383U, 2706411211U, 3764615828U,
	3883162495U, 4051445305U, 2412729798U, 3299405164U, 3991911166U,
	2348767304U, 2664054906U, 3763609282U,  593943581U, 3757090046U,
	2075338894U, 2020550814U, 4287452920U, 4290140003U, 1422957317U,
	2512716667U, 2003485045U, 2307520103U, 2288472169U, 3940751663U,
	4204638664U, 2892583423U, 1710068300U, 3904755993U, 2363243951U,
	3038334120U,  547099465U,  771105860U, 3199983734U, 4282046461U,
	2298388363U,  934810218U, 2837827901U, 3952500708U, 2095130248U,
	3083335297U,   26885281U, 3932155283U, 1531751116U, 1425227133U,
	 495654159U, 3279634176U, 3855562207U, 3957195338U, 4159985527U,
	 893375062U, 1875515536U, 1327247422U, 3754140693U, 1028923197U,
	1729880440U,  805571298U,  448971099U, 2726757106U, 2749436461U,
	2485987104U,  175337042U, 3235477922U, 3882114302U, 2020970972U,
	 943926109U, 2762587195U, 1904195558U, 3452650564U,  108432281U,
	3893463573U, 3977583081U, 2636504348U, 1110673525U, 3548479841U,
	4258854744U,  980047703U, 4057175418U, 3890008292U,  145653646U,
	3141868989U, 3293216228U, 1194331837U, 1254570642U, 3049934521U,
	2868313360U, 2886032750U, 1110873820U,  279553524U, 3007258565U,
	1104807822U, 3186961098U,  315764646U, 2163680838U, 3574508994U,
	3099755655U,  191957684U, 3642656737U, 3317946149U, 3522087636U,
	 444526410U,  779157624U, 1088229627U, 1092460223U, 1856013765U,
	3659877367U,  368270451U,  503570716U, 3000984671U, 2742789647U,
	 928097709U, 2914109539U,  308843566U, 2816161253U, 3667192079U,
	2762679057U, 3395240989U, 2928925038U, 1491465914U, 3458702834U,
	3787782576U, 2894104823U, 1296880455U, 1253636503U,  989959407U,
	2291560361U, 2776790436U, 1913178042U, 1584677829U,  689637520U,
	1898406878U,  688391508U, 3385234998U,  845493284U, 1943591856U,
	2720472050U,  222695101U, 1653320868U, 2904632120U, 4084936008U,
	1080720688U, 3938032556U,  387896427U, 2650839632U,   99042991U,
	1720913794U, 1047186003U, 1877048040U, 2090457659U,  517087501U,
	4172014665U, 2129713163U, 2413533132U, 2760285054U, 4129272496U,
	1317737175U, 2309566414U, 2228873332U, 3889671280U, 1110864630U,
	3576797776U, 2074552772U,  832002644U, 3097122623U, 2464859298U,
	2679603822U, 1667489885U, 3237652716U, 1478413938U, 1719340335U,
	2306631119U,  639727358U, 3369698270U,  226902796U, 2099920751U,
	1892289957U, 2201594097U, 3508197013U, 3495811856U, 3900381493U,
	 841660320U, 3974501451U, 3360949056U, 1676829340U,  728899254U,
	2047809627U, 2390948962U,  670165943U, 3412951831U, 4189320049U,
	1911595255U, 2055363086U,  507170575U,  418219594U, 4141495280U,
	2692088692U, 4203630654U, 3540093932U,  791986533U, 2237921051U,
	2526864324U, 2956616642U, 1394958700U, 1983768223U, 1893373266U,
	 591653646U,  228432437U, 1611046598U, 3007736357U, 1040040725U,
	2726180733U, 2789804360U, 4263568405U,  829098158U, 3847722805U,
	1123578029U, 1804276347U,  997971319U, 4203797076U, 4185199713U,
	2811733626U, 2343642194U, 2985262313U, 1417930827U, 3759587724U,
	1967077982U, 1585223204U, 1097475516U, 1903944948U,  740382444U,
	1114142065U, 1541796065U, 1718384172U, 1544076191U, 1134682254U,
	3519754455U, 2866243923U,  341865437U,  645498576U, 2690735853U,
	1046963033U, 2493178460U, 1187604696U, 1619577821U,  488503634U,
	3255768161U, 2306666149U, 1630514044U, 2377698367U, 2751503746U,
	3794467088U, 1796415981U, 3657173746U,  409136296U, 1387122342U,
	1297726519U,  219544855U, 4270285558U,  437578827U, 1444698679U,
	2258519491U,  963109892U, 3982244073U, 3351535275U,  385328496U,
	1804784013U,  698059346U, 3920535147U,  708331212U,  784338163U,
	 785678147U, 1238376158U, 1557298846U, 2037809321U,  271576218U,
	4145155269U, 1913481602U, 2763691931U,  588981080U, 1201098051U,
	3717640232U, 1509206239U,  662536967U, 3180523616U, 1133105435U,
	2963500837U, 2253971215U, 3153642623U, 1066925709U, 2582781958U,
	3034720222U, 1090798544U, 2942170004U, 4036187520U,  686972531U,
	2610990302U, 2641437026U, 1837562420U,  722096247U, 1315333033U,
	2102231203U, 3402389208U, 3403698140U, 1312402831U, 2898426558U,
	 814384596U,  385649582U, 1916643285U, 1924625106U, 2512905582U,
	2501170304U, 4275223366U, 2841225246U, 1467663688U, 3563567847U,
	2969208552U,  884750901U,  102992576U,  227844301U, 3681442994U,
	3502881894U, 4034693299U, 1166727018U, 1697460687U, 1737778332U,
	1787161139U, 1053003655U, 1215024478U, 2791616766U, 2525841204U,
	1629323443U,    3233815U, 2003823032U, 3083834263U, 2379264872U,
	3752392312U, 1287475550U, 3770904171U, 3004244617U, 1502117784U,
	 918698423U, 2419857538U, 3864502062U, 1751322107U, 2188775056U,
	4018728324U,  983712955U,  440071928U, 3710838677U, 2001027698U,
	3994702151U,   22493119U, 3584400918U, 3446253670U, 4254789085U,
	1405447860U, 1240245579U, 1800644159U, 1661363424U, 3278326132U,
	3403623451U,   67092802U, 2609352193U, 3914150340U, 1814842761U,
	3610830847U,  591531412U, 3880232807U, 1673505890U, 2585326991U,
	1678544474U, 3148435887U, 3457217359U, 1193226330U, 2816576908U,
	 154025329U,  121678860U, 1164915738U,  973873761U,  269116100U,
	  52087970U,  744015362U,  498556057U,   94298882U, 1563271621U,
	2383059628U, 4197367290U, 3958472990U, 2592083636U, 2906408439U,
	1097742433U, 3924840517U,  264557272U, 2292287003U, 3203307984U,
	4047038857U, 3820609705U, 2333416067U, 1839206046U, 3600944252U,
	3412254904U,  583538222U, 2390557166U, 4140459427U, 2810357445U,
	 226777499U, 2496151295U, 2207301712U, 3283683112U,  611630281U,
	1933218215U, 3315610954U, 3889441987U, 3719454256U, 3957190521U,
	1313998161U, 2365383016U, 3146941060U, 1801206260U,  796124080U,
	2076248581U, 1747472464U, 3254365145U,  595543130U, 3573909503U,
	3758250204U, 2020768540U, 2439254210U,   93368951U, 3155792250U,
	2600232980U, 3709198295U, 3894900440U, 2971850836U, 1578909644U,
	1443493395U, 2581621665U, 3086506297U, 2443465861U,  558107211U,
	1519367835U,  249149686U,  908102264U, 2588765675U, 1232743965U,
	1001330373U, 3561331654U, 2259301289U, 1564977624U, 3835077093U,
	 727244906U, 4255738067U, 1214133513U, 2570786021U, 3899704621U,
	1633861986U, 1636979509U, 1438500431U,   58463278U, 2823485629U,
	2297430187U, 2926781924U, 3371352948U, 1864009023U, 2722267973U,
	1444292075U,  437703973U, 1060414512U,  189705863U,  910018135U,
	4077357964U,  884213423U, 2644986052U, 3973488374U, 1187906116U,
	2331207875U,  780463700U, 3713351662U, 3854611290U,  412805574U,
	2978462572U, 2176222820U,  829424696U, 2790788332U, 2750819108U,
	1594611657U, 3899878394U, 3032870364U, 1702887682U, 1948167778U,
	  14130042U,  192292500U,  947227076U,   90719497U, 3854230320U,
	 784028434U, 2142399787U, 1563449646U, 2844400217U,  819143172U,
	2883302356U, 2328055304U, 1328532246U, 2603885363U, 3375188924U,
	 933941291U, 3627039714U, 2129697284U, 2167253953U, 2506905438U,
	1412424497U, 2981395985U, 1418359660U, 2925902456U,   52752784U,
	3713667988U, 3924669405U,  648975707U, 1145520213U, 4018650664U,
	3805915440U, 2380542088U, 2013260958U, 3262572197U, 2465078101U,
	1114540067U, 3728768081U, 2396958768U,  590672271U,  904818725U,
	4263660715U,  700754408U, 1042601829U, 4094111823U, 4274838909U,
	2512692617U, 2774300207U, 2057306915U, 3470942453U,   99333088U,
	1142661026U, 2889931380U,   14316674U, 2201179167U,  415289459U,
	 448265759U, 3515142743U, 3254903683U,  246633281U, 1184307224U,
	2418347830U, 2092967314U, 2682072314U, 2558750234U, 2000352263U,
	1544150531U,  399010405U, 1513946097U,  499682937U,  461167460U,
	3045570638U, 1633669705U,  851492362U, 4052801922U, 2055266765U,
	 635556996U,  368266356U, 2385737383U, 3218202352U, 2603772408U,
	 349178792U,  226482567U, 3102426060U, 3575998268U, 2103001871U,
	3243137071U,  225500688U, 1634718593U, 4283311431U, 4292122923U,
	3842802787U,  811735523U,  105712518U,  663434053U, 1855889273U,
	2847972595U, 1196355421U, 2552150115U, 4254510614U, 3752181265U,
	3430721819U, 3828705396U, 3436287905U, 3441964937U, 4123670631U,
	 353001539U,  459496439U, 3799690868U, 1293777660U, 2761079737U,
	 498096339U, 3398433374U, 4080378380U, 2304691596U, 2995729055U,
	4134660419U, 3903444024U, 3576494993U,  203682175U, 3321164857U,
	2747963611U,   79749085U, 2992890370U, 1240278549U, 1772175713U,
	2111331972U, 2655023449U, 1683896345U, 2836027212U, 3482868021U,
	2489884874U,  756853961U, 2298874501U, 4013448667U, 4143996022U,
	2948306858U, 4132920035U, 1283299272U,  995592228U, 3450508595U,
	1027845759U, 1766942720U, 3861411826U, 1446861231U,   95974993U,
	3502263554U, 1487532194U,  601502472U, 4129619129U,  250131773U,
	2050079547U, 3198903947U, 3105589778U, 4066481316U, 3026383978U,
	2276901713U,  365637751U, 2260718426U, 1394775634U, 1791172338U,
	2690503163U, 2952737846U, 1568710462U,  732623190U, 2980358000U,
	1053631832U, 1432426951U, 3229149635U, 1854113985U, 3719733532U,
	3204031934U,  735775531U,  107468620U, 3734611984U,  631009402U,
	3083622457U, 4109580626U,  159373458U, 1301970201U, 4132389302U,
	1293255004U,  847182752U, 4170022737U,   96712900U, 2641406755U,
	1381727755U,  405608287U, 4287919625U, 1703554290U, 3589580244U,
	2911403488U,    2166565U, 2647306451U, 2330535117U, 1200815358U,
	1165916754U,  245060911U, 4040679071U, 3684908771U, 2452834126U,
	2486872773U, 2318678365U, 2940627908U, 1837837240U, 3447897409U,
	4270484676U, 1495388728U, 3754288477U, 4204167884U, 1386977705U,
	2692224733U, 3076249689U, 4109568048U, 4170955115U, 4167531356U,
	4020189950U, 4261855038U, 3036907575U, 3410399885U, 3076395737U,
	1046178638U,  144496770U,  230725846U, 3349637149U,   17065717U,
	2809932048U, 2054581785U, 3608424964U, 3259628808U,  134897388U,
	3743067463U,  257685904U, 3795656590U, 1562468719U, 3589103904U,
	3120404710U,  254684547U, 2653661580U, 3663904795U, 2631942758U,
	1063234347U, 2609732900U, 2332080715U, 3521125233U, 1180599599U,
	1935868586U, 4110970440U,  296706371U, 2128666368U, 1319875791U,
	1570900197U, 3096025483U, 1799882517U, 1928302007U, 1163707758U,
	1244491489U, 3533770203U,  567496053U, 2757924305U, 2781639343U,
	2818420107U,  560404889U, 2619609724U, 4176035430U, 2511289753U,
	2521842019U, 3910553502U, 2926149387U, 3302078172U, 4237118867U,
	 330725126U,  367400677U,  888239854U,  545570454U, 4259590525U,
	 134343617U, 1102169784U, 1647463719U, 3260979784U, 1518840883U,
	3631537963U, 3342671457U, 1301549147U, 2083739356U,  146593792U,
	3217959080U,  652755743U, 2032187193U, 3898758414U, 1021358093U,
	4037409230U, 2176407931U, 3427391950U, 2883553603U,  985613827U,
	3105265092U, 3423168427U, 3387507672U,  467170288U, 2141266163U,
	3723870208U,  916410914U, 1293987799U, 2652584950U,  769160137U,
	3205292896U, 1561287359U, 1684510084U, 3136055621U, 3765171391U,
	 639683232U, 2639569327U, 1218546948U, 4263586685U, 3058215773U,
	2352279820U,  401870217U, 2625822463U, 1529125296U, 2981801895U,
	1191285226U, 4027725437U, 3432700217U, 4098835661U,  971182783U,
	2443861173U, 3881457123U, 3874386651U,  457276199U, 2638294160U,
	4002809368U,  421169044U, 1112642589U, 3076213779U, 3387033971U,
	2499610950U, 3057240914U, 1662679783U,  461224431U, 1168395933U
};
static const uint32_t init_by_array_32_expected[] = {
	2920711183U, 3885745737U, 3501893680U,  856470934U, 1421864068U,
	 277361036U, 1518638004U, 2328404353U, 3355513634U,   64329189U,
	1624587673U, 3508467182U, 2481792141U, 3706480799U, 1925859037U,
	2913275699U,  882658412U,  384641219U,  422202002U, 1873384891U,
	2006084383U, 3924929912U, 1636718106U, 3108838742U, 1245465724U,
	4195470535U,  779207191U, 1577721373U, 1390469554U, 2928648150U,
	 121399709U, 3170839019U, 4044347501U,  953953814U, 3821710850U,
	3085591323U, 3666535579U, 3577837737U, 2012008410U, 3565417471U,
	4044408017U,  433600965U, 1637785608U, 1798509764U,  860770589U,
	3081466273U, 3982393409U, 2451928325U, 3437124742U, 4093828739U,
	3357389386U, 2154596123U,  496568176U, 2650035164U, 2472361850U,
	   3438299U, 2150366101U, 1577256676U, 3802546413U, 1787774626U,
	4078331588U, 3706103141U,  170391138U, 3806085154U, 1680970100U,
	1961637521U, 3316029766U,  890610272U, 1453751581U, 1430283664U,
	3051057411U, 3597003186U,  542563954U, 3796490244U, 1690016688U,
	3448752238U,  440702173U,  347290497U, 1121336647U, 2540588620U,
	 280881896U, 2495136428U,  213707396U,   15104824U, 2946180358U,
	 659000016U,  566379385U, 2614030979U, 2855760170U,  334526548U,
	2315569495U, 2729518615U,  564745877U, 1263517638U, 3157185798U,
	1604852056U, 1011639885U, 2950579535U, 2524219188U,  312951012U,
	1528896652U, 1327861054U, 2846910138U, 3966855905U, 2536721582U,
	 855353911U, 1685434729U, 3303978929U, 1624872055U, 4020329649U,
	3164802143U, 1642802700U, 1957727869U, 1792352426U, 3334618929U,
	2631577923U, 3027156164U,  842334259U, 3353446843U, 1226432104U,
	1742801369U, 3552852535U, 3471698828U, 1653910186U, 3380330939U,
	2313782701U, 3351007196U, 2129839995U, 1800682418U, 4085884420U,
	1625156629U, 3669701987U,  615211810U, 3294791649U, 4131143784U,
	2590843588U, 3207422808U, 3275066464U,  561592872U, 3957205738U,
	3396578098U,   48410678U, 3505556445U, 1005764855U, 3920606528U,
	2936980473U, 2378918600U, 2404449845U, 1649515163U,  701203563U,
	3705256349U,   83714199U, 3586854132U,  922978446U, 2863406304U,
	3523398907U, 2606864832U, 2385399361U, 3171757816U, 4262841009U,
	3645837721U, 1169579486U, 3666433897U, 3174689479U, 1457866976U,
	3803895110U, 3346639145U, 1907224409U, 1978473712U, 1036712794U,
	 980754888U, 1302782359U, 1765252468U,  459245755U, 3728923860U,
	1512894209U, 2046491914U,  207860527U,  514188684U, 2288713615U,
	1597354672U, 3349636117U, 2357291114U, 3995796221U,  945364213U,
	1893326518U, 3770814016U, 1691552714U, 2397527410U,  967486361U,
	 776416472U, 4197661421U,  951150819U, 1852770983U, 4044624181U,
	1399439738U, 4194455275U, 2284037669U, 1550734958U, 3321078108U,
	1865235926U, 2912129961U, 2664980877U, 1357572033U, 2600196436U,
	2486728200U, 2372668724U, 1567316966U, 2374111491U, 1839843570U,
	  20815612U, 3727008608U, 3871996229U,  824061249U, 1932503978U,
	3404541726U,  758428924U, 2609331364U, 1223966026U, 1299179808U,
	 648499352U, 2180134401U,  880821170U, 3781130950U,  113491270U,
	1032413764U, 4185884695U, 2490396037U, 1201932817U, 4060951446U,
	4165586898U, 1629813212U, 2887821158U,  415045333U,  628926856U,
	2193466079U, 3391843445U, 2227540681U, 1907099846U, 2848448395U,
	1717828221U, 1372704537U, 1707549841U, 2294058813U, 2101214437U,
	2052479531U, 1695809164U, 3176587306U, 2632770465U,   81634404U,
	1603220563U,  644238487U,  302857763U,  897352968U, 2613146653U,
	1391730149U, 4245717312U, 4191828749U, 1948492526U, 2618174230U,
	3992984522U, 2178852787U, 3596044509U, 3445573503U, 2026614616U,
	 915763564U, 3415689334U, 2532153403U, 3879661562U, 2215027417U,
	3111154986U, 2929478371U,  668346391U, 1152241381U, 2632029711U,
	3004150659U, 2135025926U,  948690501U, 2799119116U, 4228829406U,
	1981197489U, 4209064138U,  684318751U, 3459397845U,  201790843U,
	4022541136U, 3043635877U,  492509624U, 3263466772U, 1509148086U,
	 921459029U, 3198857146U,  705479721U, 3835966910U, 3603356465U,
	 576159741U, 1742849431U,  594214882U, 2055294343U, 3634861861U,
	 449571793U, 3246390646U, 3868232151U, 1479156585U, 2900125656U,
	2464815318U, 3960178104U, 1784261920U,   18311476U, 3627135050U,
	 644609697U,  424968996U,  919890700U, 2986824110U,  816423214U,
	4003562844U, 1392714305U, 1757384428U, 2569030598U,  995949559U,
	3875659880U, 2933807823U, 2752536860U, 2993858466U, 4030558899U,
	2770783427U, 2775406005U, 2777781742U, 1931292655U,  472147933U,
	3865853827U, 2726470545U, 2668412860U, 2887008249U,  408979190U,
	3578063323U, 3242082049U, 1778193530U,   27981909U, 2362826515U,
	 389875677U, 1043878156U,  581653903U, 3830568952U,  389535942U,
	3713523185U, 2768373359U, 2526101582U, 1998618197U, 1160859704U,
	3951172488U, 1098005003U,  906275699U, 3446228002U, 2220677963U,
	2059306445U,  132199571U,  476838790U, 1868039399U, 3097344807U,
	 857300945U,  396345050U, 2835919916U, 1782168828U, 1419519470U,
	4288137521U,  819087232U,  596301494U,  872823172U, 1526888217U,
	 805161465U, 1116186205U, 2829002754U, 2352620120U,  620121516U,
	 354159268U, 3601949785U,  209568138U, 1352371732U, 2145977349U,
	4236871834U, 1539414078U, 3558126206U, 3224857093U, 4164166682U,
	3817553440U, 3301780278U, 2682696837U, 3734994768U, 1370950260U,
	1477421202U, 2521315749U, 1330148125U, 1261554731U, 2769143688U,
	3554756293U, 4235882678U, 3254686059U, 3530579953U, 1215452615U,
	3574970923U, 4057131421U,  589224178U, 1000098193U,  171190718U,
	2521852045U, 2351447494U, 2284441580U, 2646685513U, 3486933563U,
	3789864960U, 1190528160U, 1702536782U, 1534105589U, 4262946827U,
	2726686826U, 3584544841U, 2348270128U, 2145092281U, 2502718509U,
	1027832411U, 3571171153U, 1287361161U, 4011474411U, 3241215351U,
	2419700818U,  971242709U, 1361975763U, 1096842482U, 3271045537U,
	  81165449U,  612438025U, 3912966678U, 1356929810U,  733545735U,
	 537003843U, 1282953084U,  884458241U,  588930090U, 3930269801U,
	2961472450U, 1219535534U, 3632251943U,  268183903U, 1441240533U,
	3653903360U, 3854473319U, 2259087390U, 2548293048U, 2022641195U,
	2105543911U, 1764085217U, 3246183186U,  482438805U,  888317895U,
	2628314765U, 2466219854U,  717546004U, 2322237039U,  416725234U,
	1544049923U, 1797944973U, 3398652364U, 3111909456U,  485742908U,
	2277491072U, 1056355088U, 3181001278U,  129695079U, 2693624550U,
	1764438564U, 3797785470U,  195503713U, 3266519725U, 2053389444U,
	1961527818U, 3400226523U, 3777903038U, 2597274307U, 4235851091U,
	4094406648U, 2171410785U, 1781151386U, 1378577117U,  654643266U,
	3424024173U, 3385813322U,  679385799U,  479380913U,  681715441U,
	3096225905U,  276813409U, 3854398070U, 2721105350U,  831263315U,
	3276280337U, 2628301522U, 3984868494U, 1466099834U, 2104922114U,
	1412672743U,  820330404U, 3491501010U,  942735832U,  710652807U,
	3972652090U,  679881088U,   40577009U, 3705286397U, 2815423480U,
	3566262429U,  663396513U, 3777887429U, 4016670678U,  404539370U,
	1142712925U, 1140173408U, 2913248352U, 2872321286U,  263751841U,
	3175196073U, 3162557581U, 2878996619U,   75498548U, 3836833140U,
	3284664959U, 1157523805U,  112847376U,  207855609U, 1337979698U,
	1222578451U,  157107174U,  901174378U, 3883717063U, 1618632639U,
	1767889440U, 4264698824U, 1582999313U,  884471997U, 2508825098U,
	3756370771U, 2457213553U, 3565776881U, 3709583214U,  915609601U,
	 460833524U, 1091049576U,   85522880U,    2553251U,  132102809U,
	2429882442U, 2562084610U, 1386507633U, 4112471229U,   21965213U,
	1981516006U, 2418435617U, 3054872091U, 4251511224U, 2025783543U,
	1916911512U, 2454491136U, 3938440891U, 3825869115U, 1121698605U,
	3463052265U,  802340101U, 1912886800U, 4031997367U, 3550640406U,
	1596096923U,  610150600U,  431464457U, 2541325046U,  486478003U,
	 739704936U, 2862696430U, 3037903166U, 1129749694U, 2611481261U,
	1228993498U,  510075548U, 3424962587U, 2458689681U,  818934833U,
	4233309125U, 1608196251U, 3419476016U, 1858543939U, 2682166524U,
	3317854285U,  631986188U, 3008214764U,  613826412U, 3567358221U,
	3512343882U, 1552467474U, 3316162670U, 1275841024U, 4142173454U,
	 565267881U,  768644821U,  198310105U, 2396688616U, 1837659011U,
	 203429334U,  854539004U, 4235811518U, 3338304926U, 3730418692U,
	3852254981U, 3032046452U, 2329811860U, 2303590566U, 2696092212U,
	3894665932U,  145835667U,  249563655U, 1932210840U, 2431696407U,
	3312636759U,  214962629U, 2092026914U, 3020145527U, 4073039873U,
	2739105705U, 1308336752U,  855104522U, 2391715321U,   67448785U,
	 547989482U,  854411802U, 3608633740U,  431731530U,  537375589U,
	3888005760U,  696099141U,  397343236U, 1864511780U,   44029739U,
	1729526891U, 1993398655U, 2010173426U, 2591546756U,  275223291U,
	1503900299U, 4217765081U, 2185635252U, 1122436015U, 3550155364U,
	 681707194U, 3260479338U,  933579397U, 2983029282U, 2505504587U,
	2667410393U, 2962684490U, 4139721708U, 2658172284U, 2452602383U,
	2607631612U, 1344296217U, 3075398709U, 2949785295U, 1049956168U,
	3917185129U, 2155660174U, 3280524475U, 1503827867U,  674380765U,
	1918468193U, 3843983676U,  634358221U, 2538335643U, 1873351298U,
	3368723763U, 2129144130U, 3203528633U, 3087174986U, 2691698871U,
	2516284287U,   24437745U, 1118381474U, 2816314867U, 2448576035U,
	4281989654U,  217287825U,  165872888U, 2628995722U, 3533525116U,
	2721669106U,  872340568U, 3429930655U, 3309047304U, 3916704967U,
	3270160355U, 1348884255U, 1634797670U,  881214967U, 4259633554U,
	 174613027U, 1103974314U, 1625224232U, 2678368291U, 1133866707U,
	3853082619U, 4073196549U, 1189620777U,  637238656U,  930241537U,
	4042750792U, 3842136042U, 2417007212U, 2524907510U, 1243036827U,
	1282059441U, 3764588774U, 1394459615U, 2323620015U, 1166152231U,
	3307479609U, 3849322257U, 3507445699U, 4247696636U,  758393720U,
	 967665141U, 1095244571U, 1319812152U,  407678762U, 2640605208U,
	2170766134U, 3663594275U, 4039329364U, 2512175520U,  725523154U,
	2249807004U, 3312617979U, 2414634172U, 1278482215U,  349206484U,
	1573063308U, 1196429124U, 3873264116U, 2400067801U,  268795167U,
	 226175489U, 2961367263U, 1968719665U,   42656370U, 1010790699U,
	 561600615U, 2422453992U, 3082197735U, 1636700484U, 3977715296U,
	3125350482U, 3478021514U, 2227819446U, 1540868045U, 3061908980U,
	1087362407U, 3625200291U,  361937537U,  580441897U, 1520043666U,
	2270875402U, 1009161260U, 2502355842U, 4278769785U,  473902412U,
	1057239083U, 1905829039U, 1483781177U, 2080011417U, 1207494246U,
	1806991954U, 2194674403U, 3455972205U,  807207678U, 3655655687U,
	 674112918U,  195425752U, 3917890095U, 1874364234U, 1837892715U,
	3663478166U, 1548892014U, 2570748714U, 2049929836U, 2167029704U,
	 697543767U, 3499545023U, 3342496315U, 1725251190U, 3561387469U,
	2905606616U, 1580182447U, 3934525927U, 4103172792U, 1365672522U,
	1534795737U, 3308667416U, 2841911405U, 3943182730U, 4072020313U,
	3494770452U, 3332626671U,   55327267U,  478030603U,  411080625U,
	3419529010U, 1604767823U, 3513468014U,  570668510U,  913790824U,
	2283967995U,  695159462U, 3825542932U, 4150698144U, 1829758699U,
	 202895590U, 1609122645U, 1267651008U, 2910315509U, 2511475445U,
	2477423819U, 3932081579U,  900879979U, 2145588390U, 2670007504U,
	 580819444U, 1864996828U, 2526325979U, 1019124258U,  815508628U,
	2765933989U, 1277301341U, 3006021786U,  855540956U,  288025710U,
	1919594237U, 2331223864U,  177452412U, 2475870369U, 2689291749U,
	 865194284U,  253432152U, 2628531804U, 2861208555U, 2361597573U,
	1653952120U, 1039661024U, 2159959078U, 3709040440U, 3564718533U,
	2596878672U, 2041442161U,   31164696U, 2662962485U, 3665637339U,
	1678115244U, 2699839832U, 3651968520U, 3521595541U,  458433303U,
	2423096824U,   21831741U,  380011703U, 2498168716U,  861806087U,
	1673574843U, 4188794405U, 2520563651U, 2632279153U, 2170465525U,
	4171949898U, 3886039621U, 1661344005U, 3424285243U,  992588372U,
	2500984144U, 2993248497U, 3590193895U, 1535327365U,  515645636U,
	 131633450U, 3729760261U, 1613045101U, 3254194278U,   15889678U,
	1493590689U,  244148718U, 2991472662U, 1401629333U,  777349878U,
	2501401703U, 4285518317U, 3794656178U,  955526526U, 3442142820U,
	3970298374U,  736025417U, 2737370764U, 1271509744U,  440570731U,
	 136141826U, 1596189518U,  923399175U,  257541519U, 3505774281U,
	2194358432U, 2518162991U, 1379893637U, 2667767062U, 3748146247U,
	1821712620U, 3923161384U, 1947811444U, 2392527197U, 4127419685U,
	1423694998U, 4156576871U, 1382885582U, 3420127279U, 3617499534U,
	2994377493U, 4038063986U, 1918458672U, 2983166794U, 4200449033U,
	 353294540U, 1609232588U,  243926648U, 2332803291U,  507996832U,
	2392838793U, 4075145196U, 2060984340U, 4287475136U,   88232602U,
	2491531140U, 4159725633U, 2272075455U,  759298618U,  201384554U,
	 838356250U, 1416268324U,  674476934U,   90795364U,  141672229U,
	3660399588U, 4196417251U, 3249270244U, 3774530247U,   59587265U,
	3683164208U,   19392575U, 1463123697U, 1882205379U,  293780489U,
	2553160622U, 2933904694U,  675638239U, 2851336944U, 1435238743U,
	2448730183U,  804436302U, 2119845972U,  322560608U, 4097732704U,
	2987802540U,  641492617U, 2575442710U, 4217822703U, 3271835300U,
	2836418300U, 3739921620U, 2138378768U, 2879771855U, 4294903423U,
	3121097946U, 2603440486U, 2560820391U, 1012930944U, 2313499967U,
	 584489368U, 3431165766U,  897384869U, 2062537737U, 2847889234U,
	3742362450U, 2951174585U, 4204621084U, 1109373893U, 3668075775U,
	2750138839U, 3518055702U,  733072558U, 4169325400U,  788493625U
};
static const uint64_t init_gen_rand_64_expected[] = {
	KQU(16924766246869039260), KQU( 8201438687333352714),
	KQU( 2265290287015001750), KQU(18397264611805473832),
	KQU( 3375255223302384358), KQU( 6345559975416828796),
	KQU(18229739242790328073), KQU( 7596792742098800905),
	KQU(  255338647169685981), KQU( 2052747240048610300),
	KQU(18328151576097299343), KQU(12472905421133796567),
	KQU(11315245349717600863), KQU(16594110197775871209),
	KQU(15708751964632456450), KQU(10452031272054632535),
	KQU(11097646720811454386), KQU( 4556090668445745441),
	KQU(17116187693090663106), KQU(14931526836144510645),
	KQU( 9190752218020552591), KQU( 9625800285771901401),
	KQU(13995141077659972832), KQU( 5194209094927829625),
	KQU( 4156788379151063303), KQU( 8523452593770139494),
	KQU(14082382103049296727), KQU( 2462601863986088483),
	KQU( 3030583461592840678), KQU( 5221622077872827681),
	KQU( 3084210671228981236), KQU(13956758381389953823),
	KQU(13503889856213423831), KQU(15696904024189836170),
	KQU( 4612584152877036206), KQU( 6231135538447867881),
	KQU(10172457294158869468), KQU( 6452258628466708150),
	KQU(14044432824917330221), KQU(  370168364480044279),
	KQU(10102144686427193359), KQU(  667870489994776076),
	KQU( 2732271956925885858), KQU(18027788905977284151),
	KQU(15009842788582923859), KQU( 7136357960180199542),
	KQU(15901736243475578127), KQU(16951293785352615701),
	KQU(10551492125243691632), KQU(17668869969146434804),
	KQU(13646002971174390445), KQU( 9804471050759613248),
	KQU( 5511670439655935493), KQU(18103342091070400926),
	KQU(17224512747665137533), KQU(15534627482992618168),
	KQU( 1423813266186582647), KQU(15821176807932930024),
	KQU(   30323369733607156), KQU(11599382494723479403),
	KQU(  653856076586810062), KQU( 3176437395144899659),
	KQU(14028076268147963917), KQU(16156398271809666195),
	KQU( 3166955484848201676), KQU( 5746805620136919390),
	KQU(17297845208891256593), KQU(11691653183226428483),
	KQU(17900026146506981577), KQU(15387382115755971042),
	KQU(16923567681040845943), KQU( 8039057517199388606),
	KQU(11748409241468629263), KQU(  794358245539076095),
	KQU(13438501964693401242), KQU(14036803236515618962),
	KQU( 5252311215205424721), KQU(17806589612915509081),
	KQU( 6802767092397596006), KQU(14212120431184557140),
	KQU( 1072951366761385712), KQU(13098491780722836296),
	KQU( 9466676828710797353), KQU(12673056849042830081),
	KQU(12763726623645357580), KQU(16468961652999309493),
	KQU(15305979875636438926), KQU(17444713151223449734),
	KQU( 5692214267627883674), KQU(13049589139196151505),
	KQU(  880115207831670745), KQU( 1776529075789695498),
	KQU(16695225897801466485), KQU(10666901778795346845),
	KQU( 6164389346722833869), KQU( 2863817793264300475),
	KQU( 9464049921886304754), KQU( 3993566636740015468),
	KQU( 9983749692528514136), KQU(16375286075057755211),
	KQU(16042643417005440820), KQU(11445419662923489877),
	KQU( 7999038846885158836), KQU( 6721913661721511535),
	KQU( 5363052654139357320), KQU( 1817788761173584205),
	KQU(13290974386445856444), KQU( 4650350818937984680),
	KQU( 8219183528102484836), KQU( 1569862923500819899),
	KQU( 4189359732136641860), KQU(14202822961683148583),
	KQU( 4457498315309429058), KQU(13089067387019074834),
	KQU(11075517153328927293), KQU(10277016248336668389),
	KQU( 7070509725324401122), KQU(17808892017780289380),
	KQU(13143367339909287349), KQU( 1377743745360085151),
	KQU( 5749341807421286485), KQU(14832814616770931325),
	KQU( 7688820635324359492), KQU(10960474011539770045),
	KQU(   81970066653179790), KQU(12619476072607878022),
	KQU( 4419566616271201744), KQU(15147917311750568503),
	KQU( 5549739182852706345), KQU( 7308198397975204770),
	KQU(13580425496671289278), KQU(17070764785210130301),
	KQU( 8202832846285604405), KQU( 6873046287640887249),
	KQU( 6927424434308206114), KQU( 6139014645937224874),
	KQU(10290373645978487639), KQU(15904261291701523804),
	KQU( 9628743442057826883), KQU(18383429096255546714),
	KQU( 4977413265753686967), KQU( 7714317492425012869),
	KQU( 9025232586309926193), KQU(14627338359776709107),
	KQU(14759849896467790763), KQU(10931129435864423252),
	KQU( 4588456988775014359), KQU(10699388531797056724),
	KQU(  468652268869238792), KQU( 5755943035328078086),
	KQU( 2102437379988580216), KQU( 9986312786506674028),
	KQU( 2654207180040945604), KQU( 8726634790559960062),
	KQU(  100497234871808137), KQU( 2800137176951425819),
	KQU( 6076627612918553487), KQU( 5780186919186152796),
	KQU( 8179183595769929098), KQU( 6009426283716221169),
	KQU( 2796662551397449358), KQU( 1756961367041986764),
	KQU( 6972897917355606205), KQU(14524774345368968243),
	KQU( 2773529684745706940), KQU( 4853632376213075959),
	KQU( 4198177923731358102), KQU( 8271224913084139776),
	KQU( 2741753121611092226), KQU(16782366145996731181),
	KQU(15426125238972640790), KQU(13595497100671260342),
	KQU( 3173531022836259898), KQU( 6573264560319511662),
	KQU(18041111951511157441), KQU( 2351433581833135952),
	KQU( 3113255578908173487), KQU( 1739371330877858784),
	KQU(16046126562789165480), KQU( 8072101652214192925),
	KQU(15267091584090664910), KQU( 9309579200403648940),
	KQU( 5218892439752408722), KQU(14492477246004337115),
	KQU(17431037586679770619), KQU( 7385248135963250480),
	KQU( 9580144956565560660), KQU( 4919546228040008720),
	KQU(15261542469145035584), KQU(18233297270822253102),
	KQU( 5453248417992302857), KQU( 9309519155931460285),
	KQU(10342813012345291756), KQU(15676085186784762381),
	KQU(15912092950691300645), KQU( 9371053121499003195),
	KQU( 9897186478226866746), KQU(14061858287188196327),
	KQU(  122575971620788119), KQU(12146750969116317754),
	KQU( 4438317272813245201), KQU( 8332576791009527119),
	KQU(13907785691786542057), KQU(10374194887283287467),
	KQU( 2098798755649059566), KQU( 3416235197748288894),
	KQU( 8688269957320773484), KQU( 7503964602397371571),
	KQU(16724977015147478236), KQU( 9461512855439858184),
	KQU(13259049744534534727), KQU( 3583094952542899294),
	KQU( 8764245731305528292), KQU(13240823595462088985),
	KQU(13716141617617910448), KQU(18114969519935960955),
	KQU( 2297553615798302206), KQU( 4585521442944663362),
	KQU(17776858680630198686), KQU( 4685873229192163363),
	KQU(  152558080671135627), KQU(15424900540842670088),
	KQU(13229630297130024108), KQU(17530268788245718717),
	KQU(16675633913065714144), KQU( 3158912717897568068),
	KQU(15399132185380087288), KQU( 7401418744515677872),
	KQU(13135412922344398535), KQU( 6385314346100509511),
	KQU(13962867001134161139), KQU(10272780155442671999),
	KQU(12894856086597769142), KQU(13340877795287554994),
	KQU(12913630602094607396), KQU(12543167911119793857),
	KQU(17343570372251873096), KQU(10959487764494150545),
	KQU( 6966737953093821128), KQU(13780699135496988601),
	KQU( 4405070719380142046), KQU(14923788365607284982),
	KQU( 2869487678905148380), KQU( 6416272754197188403),
	KQU(15017380475943612591), KQU( 1995636220918429487),
	KQU( 3402016804620122716), KQU(15800188663407057080),
	KQU(11362369990390932882), KQU(15262183501637986147),
	KQU(10239175385387371494), KQU( 9352042420365748334),
	KQU( 1682457034285119875), KQU( 1724710651376289644),
	KQU( 2038157098893817966), KQU( 9897825558324608773),
	KQU( 1477666236519164736), KQU(16835397314511233640),
	KQU(10370866327005346508), KQU(10157504370660621982),
	KQU(12113904045335882069), KQU(13326444439742783008),
	KQU(11302769043000765804), KQU(13594979923955228484),
	KQU(11779351762613475968), KQU( 3786101619539298383),
	KQU( 8021122969180846063), KQU(15745904401162500495),
	KQU(10762168465993897267), KQU(13552058957896319026),
	KQU(11200228655252462013), KQU( 5035370357337441226),
	KQU( 7593918984545500013), KQU( 5418554918361528700),
	KQU( 4858270799405446371), KQU( 9974659566876282544),
	KQU(18227595922273957859), KQU( 2772778443635656220),
	KQU(14285143053182085385), KQU( 9939700992429600469),
	KQU(12756185904545598068), KQU( 2020783375367345262),
	KQU(   57026775058331227), KQU(  950827867930065454),
	KQU( 6602279670145371217), KQU( 2291171535443566929),
	KQU( 5832380724425010313), KQU( 1220343904715982285),
	KQU(17045542598598037633), KQU(15460481779702820971),
	KQU(13948388779949365130), KQU(13975040175430829518),
	KQU(17477538238425541763), KQU(11104663041851745725),
	KQU(15860992957141157587), KQU(14529434633012950138),
	KQU( 2504838019075394203), KQU( 7512113882611121886),
	KQU( 4859973559980886617), KQU( 1258601555703250219),
	KQU(15594548157514316394), KQU( 4516730171963773048),
	KQU(11380103193905031983), KQU( 6809282239982353344),
	KQU(18045256930420065002), KQU( 2453702683108791859),
	KQU(  977214582986981460), KQU( 2006410402232713466),
	KQU( 6192236267216378358), KQU( 3429468402195675253),
	KQU(18146933153017348921), KQU(17369978576367231139),
	KQU( 1246940717230386603), KQU(11335758870083327110),
	KQU(14166488801730353682), KQU( 9008573127269635732),
	KQU(10776025389820643815), KQU(15087605441903942962),
	KQU( 1359542462712147922), KQU(13898874411226454206),
	KQU(17911176066536804411), KQU( 9435590428600085274),
	KQU(  294488509967864007), KQU( 8890111397567922046),
	KQU( 7987823476034328778), KQU(13263827582440967651),
	KQU( 7503774813106751573), KQU(14974747296185646837),
	KQU( 8504765037032103375), KQU(17340303357444536213),
	KQU( 7704610912964485743), KQU( 8107533670327205061),
	KQU( 9062969835083315985), KQU(16968963142126734184),
	KQU(12958041214190810180), KQU( 2720170147759570200),
	KQU( 2986358963942189566), KQU(14884226322219356580),
	KQU(  286224325144368520), KQU(11313800433154279797),
	KQU(18366849528439673248), KQU(17899725929482368789),
	KQU( 3730004284609106799), KQU( 1654474302052767205),
	KQU( 5006698007047077032), KQU( 8196893913601182838),
	KQU(15214541774425211640), KQU(17391346045606626073),
	KQU( 8369003584076969089), KQU( 3939046733368550293),
	KQU(10178639720308707785), KQU( 2180248669304388697),
	KQU(   62894391300126322), KQU( 9205708961736223191),
	KQU( 6837431058165360438), KQU( 3150743890848308214),
	KQU(17849330658111464583), KQU(12214815643135450865),
	KQU(13410713840519603402), KQU( 3200778126692046802),
	KQU(13354780043041779313), KQU(  800850022756886036),
	KQU(15660052933953067433), KQU( 6572823544154375676),
	KQU(11030281857015819266), KQU(12682241941471433835),
	KQU(11654136407300274693), KQU( 4517795492388641109),
	KQU( 9757017371504524244), KQU(17833043400781889277),
	KQU(12685085201747792227), KQU(10408057728835019573),
	KQU(   98370418513455221), KQU( 6732663555696848598),
	KQU(13248530959948529780), KQU( 3530441401230622826),
	KQU(18188251992895660615), KQU( 1847918354186383756),
	KQU( 1127392190402660921), KQU(11293734643143819463),
	KQU( 3015506344578682982), KQU(13852645444071153329),
	KQU( 2121359659091349142), KQU( 1294604376116677694),
	KQU( 5616576231286352318), KQU( 7112502442954235625),
	KQU(11676228199551561689), KQU(12925182803007305359),
	KQU( 7852375518160493082), KQU( 1136513130539296154),
	KQU( 5636923900916593195), KQU( 3221077517612607747),
	KQU(17784790465798152513), KQU( 3554210049056995938),
	KQU(17476839685878225874), KQU( 3206836372585575732),
	KQU( 2765333945644823430), KQU(10080070903718799528),
	KQU( 5412370818878286353), KQU( 9689685887726257728),
	KQU( 8236117509123533998), KQU( 1951139137165040214),
	KQU( 4492205209227980349), KQU(16541291230861602967),
	KQU( 1424371548301437940), KQU( 9117562079669206794),
	KQU(14374681563251691625), KQU(13873164030199921303),
	KQU( 6680317946770936731), KQU(15586334026918276214),
	KQU(10896213950976109802), KQU( 9506261949596413689),
	KQU( 9903949574308040616), KQU( 6038397344557204470),
	KQU(  174601465422373648), KQU(15946141191338238030),
	KQU(17142225620992044937), KQU( 7552030283784477064),
	KQU( 2947372384532947997), KQU(  510797021688197711),
	KQU( 4962499439249363461), KQU(   23770320158385357),
	KQU(  959774499105138124), KQU( 1468396011518788276),
	KQU( 2015698006852312308), KQU( 4149400718489980136),
	KQU( 5992916099522371188), KQU(10819182935265531076),
	KQU(16189787999192351131), KQU(  342833961790261950),
	KQU(12470830319550495336), KQU(18128495041912812501),
	KQU( 1193600899723524337), KQU( 9056793666590079770),
	KQU( 2154021227041669041), KQU( 4963570213951235735),
	KQU( 4865075960209211409), KQU( 2097724599039942963),
	KQU( 2024080278583179845), KQU(11527054549196576736),
	KQU(10650256084182390252), KQU( 4808408648695766755),
	KQU( 1642839215013788844), KQU(10607187948250398390),
	KQU( 7076868166085913508), KQU(  730522571106887032),
	KQU(12500579240208524895), KQU( 4484390097311355324),
	KQU(15145801330700623870), KQU( 8055827661392944028),
	KQU( 5865092976832712268), KQU(15159212508053625143),
	KQU( 3560964582876483341), KQU( 4070052741344438280),
	KQU( 6032585709886855634), KQU(15643262320904604873),
	KQU( 2565119772293371111), KQU(  318314293065348260),
	KQU(15047458749141511872), KQU( 7772788389811528730),
	KQU( 7081187494343801976), KQU( 6465136009467253947),
	KQU(10425940692543362069), KQU(  554608190318339115),
	KQU(14796699860302125214), KQU( 1638153134431111443),
	KQU(10336967447052276248), KQU( 8412308070396592958),
	KQU( 4004557277152051226), KQU( 8143598997278774834),
	KQU(16413323996508783221), KQU(13139418758033994949),
	KQU( 9772709138335006667), KQU( 2818167159287157659),
	KQU(17091740573832523669), KQU(14629199013130751608),
	KQU(18268322711500338185), KQU( 8290963415675493063),
	KQU( 8830864907452542588), KQU( 1614839084637494849),
	KQU(14855358500870422231), KQU( 3472996748392519937),
	KQU(15317151166268877716), KQU( 5825895018698400362),
	KQU(16730208429367544129), KQU(10481156578141202800),
	KQU( 4746166512382823750), KQU(12720876014472464998),
	KQU( 8825177124486735972), KQU(13733447296837467838),
	KQU( 6412293741681359625), KQU( 8313213138756135033),
	KQU(11421481194803712517), KQU( 7997007691544174032),
	KQU( 6812963847917605930), KQU( 9683091901227558641),
	KQU(14703594165860324713), KQU( 1775476144519618309),
	KQU( 2724283288516469519), KQU(  717642555185856868),
	KQU( 8736402192215092346), KQU(11878800336431381021),
	KQU( 4348816066017061293), KQU( 6115112756583631307),
	KQU( 9176597239667142976), KQU(12615622714894259204),
	KQU(10283406711301385987), KQU( 5111762509485379420),
	KQU( 3118290051198688449), KQU( 7345123071632232145),
	KQU( 9176423451688682359), KQU( 4843865456157868971),
	KQU(12008036363752566088), KQU(12058837181919397720),
	KQU( 2145073958457347366), KQU( 1526504881672818067),
	KQU( 3488830105567134848), KQU(13208362960674805143),
	KQU( 4077549672899572192), KQU( 7770995684693818365),
	KQU( 1398532341546313593), KQU(12711859908703927840),
	KQU( 1417561172594446813), KQU(17045191024194170604),
	KQU( 4101933177604931713), KQU(14708428834203480320),
	KQU(17447509264469407724), KQU(14314821973983434255),
	KQU(17990472271061617265), KQU( 5087756685841673942),
	KQU(12797820586893859939), KQU( 1778128952671092879),
	KQU( 3535918530508665898), KQU( 9035729701042481301),
	KQU(14808661568277079962), KQU(14587345077537747914),
	KQU(11920080002323122708), KQU( 6426515805197278753),
	KQU( 3295612216725984831), KQU(11040722532100876120),
	KQU(12305952936387598754), KQU(16097391899742004253),
	KQU( 4908537335606182208), KQU(12446674552196795504),
	KQU(16010497855816895177), KQU( 9194378874788615551),
	KQU( 3382957529567613384), KQU( 5154647600754974077),
	KQU( 9801822865328396141), KQU( 9023662173919288143),
	KQU(17623115353825147868), KQU( 8238115767443015816),
	KQU(15811444159859002560), KQU( 9085612528904059661),
	KQU( 6888601089398614254), KQU(  258252992894160189),
	KQU( 6704363880792428622), KQU( 6114966032147235763),
	KQU(11075393882690261875), KQU( 8797664238933620407),
	KQU( 5901892006476726920), KQU( 5309780159285518958),
	KQU(14940808387240817367), KQU(14642032021449656698),
	KQU( 9808256672068504139), KQU( 3670135111380607658),
	KQU(11211211097845960152), KQU( 1474304506716695808),
	KQU(15843166204506876239), KQU( 7661051252471780561),
	KQU(10170905502249418476), KQU( 7801416045582028589),
	KQU( 2763981484737053050), KQU( 9491377905499253054),
	KQU(16201395896336915095), KQU( 9256513756442782198),
	KQU( 5411283157972456034), KQU( 5059433122288321676),
	KQU( 4327408006721123357), KQU( 9278544078834433377),
	KQU( 7601527110882281612), KQU(11848295896975505251),
	KQU(12096998801094735560), KQU(14773480339823506413),
	KQU(15586227433895802149), KQU(12786541257830242872),
	KQU( 6904692985140503067), KQU( 5309011515263103959),
	KQU(12105257191179371066), KQU(14654380212442225037),
	KQU( 2556774974190695009), KQU( 4461297399927600261),
	KQU(14888225660915118646), KQU(14915459341148291824),
	KQU( 2738802166252327631), KQU( 6047155789239131512),
	KQU(12920545353217010338), KQU(10697617257007840205),
	KQU( 2751585253158203504), KQU(13252729159780047496),
	KQU(14700326134672815469), KQU(14082527904374600529),
	KQU(16852962273496542070), KQU(17446675504235853907),
	KQU(15019600398527572311), KQU(12312781346344081551),
	KQU(14524667935039810450), KQU( 5634005663377195738),
	KQU(11375574739525000569), KQU( 2423665396433260040),
	KQU( 5222836914796015410), KQU( 4397666386492647387),
	KQU( 4619294441691707638), KQU(  665088602354770716),
	KQU(13246495665281593610), KQU( 6564144270549729409),
	KQU(10223216188145661688), KQU( 3961556907299230585),
	KQU(11543262515492439914), KQU(16118031437285993790),
	KQU( 7143417964520166465), KQU(13295053515909486772),
	KQU(   40434666004899675), KQU(17127804194038347164),
	KQU( 8599165966560586269), KQU( 8214016749011284903),
	KQU(13725130352140465239), KQU( 5467254474431726291),
	KQU( 7748584297438219877), KQU(16933551114829772472),
	KQU( 2169618439506799400), KQU( 2169787627665113463),
	KQU(17314493571267943764), KQU(18053575102911354912),
	KQU(11928303275378476973), KQU(11593850925061715550),
	KQU(17782269923473589362), KQU( 3280235307704747039),
	KQU( 6145343578598685149), KQU(17080117031114086090),
	KQU(18066839902983594755), KQU( 6517508430331020706),
	KQU( 8092908893950411541), KQU(12558378233386153732),
	KQU( 4476532167973132976), KQU(16081642430367025016),
	KQU( 4233154094369139361), KQU( 8693630486693161027),
	KQU(11244959343027742285), KQU(12273503967768513508),
	KQU(14108978636385284876), KQU( 7242414665378826984),
	KQU( 6561316938846562432), KQU( 8601038474994665795),
	KQU(17532942353612365904), KQU(17940076637020912186),
	KQU( 7340260368823171304), KQU( 7061807613916067905),
	KQU(10561734935039519326), KQU(17990796503724650862),
	KQU( 6208732943911827159), KQU(  359077562804090617),
	KQU(14177751537784403113), KQU(10659599444915362902),
	KQU(15081727220615085833), KQU(13417573895659757486),
	KQU(15513842342017811524), KQU(11814141516204288231),
	KQU( 1827312513875101814), KQU( 2804611699894603103),
	KQU(17116500469975602763), KQU(12270191815211952087),
	KQU(12256358467786024988), KQU(18435021722453971267),
	KQU(  671330264390865618), KQU(  476504300460286050),
	KQU(16465470901027093441), KQU( 4047724406247136402),
	KQU( 1322305451411883346), KQU( 1388308688834322280),
	KQU( 7303989085269758176), KQU( 9323792664765233642),
	KQU( 4542762575316368936), KQU(17342696132794337618),
	KQU( 4588025054768498379), KQU(13415475057390330804),
	KQU(17880279491733405570), KQU(10610553400618620353),
	KQU( 3180842072658960139), KQU(13002966655454270120),
	KQU( 1665301181064982826), KQU( 7083673946791258979),
	KQU(  190522247122496820), KQU(17388280237250677740),
	KQU( 8430770379923642945), KQU(12987180971921668584),
	KQU( 2311086108365390642), KQU( 2870984383579822345),
	KQU(14014682609164653318), KQU(14467187293062251484),
	KQU(  192186361147413298), KQU(15171951713531796524),
	KQU( 9900305495015948728), KQU(17958004775615466344),
	KQU(14346380954498606514), KQU(18040047357617407096),
	KQU( 5035237584833424532), KQU(15089555460613972287),
	KQU( 4131411873749729831), KQU( 1329013581168250330),
	KQU(10095353333051193949), KQU(10749518561022462716),
	KQU( 9050611429810755847), KQU(15022028840236655649),
	KQU( 8775554279239748298), KQU(13105754025489230502),
	KQU(15471300118574167585), KQU(   89864764002355628),
	KQU( 8776416323420466637), KQU( 5280258630612040891),
	KQU( 2719174488591862912), KQU( 7599309137399661994),
	KQU(15012887256778039979), KQU(14062981725630928925),
	KQU(12038536286991689603), KQU( 7089756544681775245),
	KQU(10376661532744718039), KQU( 1265198725901533130),
	KQU(13807996727081142408), KQU( 2935019626765036403),
	KQU( 7651672460680700141), KQU( 3644093016200370795),
	KQU( 2840982578090080674), KQU(17956262740157449201),
	KQU(18267979450492880548), KQU(11799503659796848070),
	KQU( 9942537025669672388), KQU(11886606816406990297),
	KQU( 5488594946437447576), KQU( 7226714353282744302),
	KQU( 3784851653123877043), KQU(  878018453244803041),
	KQU(12110022586268616085), KQU(  734072179404675123),
	KQU(11869573627998248542), KQU(  469150421297783998),
	KQU(  260151124912803804), KQU(11639179410120968649),
	KQU( 9318165193840846253), KQU(12795671722734758075),
	KQU(15318410297267253933), KQU(  691524703570062620),
	KQU( 5837129010576994601), KQU(15045963859726941052),
	KQU( 5850056944932238169), KQU(12017434144750943807),
	KQU( 7447139064928956574), KQU( 3101711812658245019),
	KQU(16052940704474982954), KQU(18195745945986994042),
	KQU( 8932252132785575659), KQU(13390817488106794834),
	KQU(11582771836502517453), KQU( 4964411326683611686),
	KQU( 2195093981702694011), KQU(14145229538389675669),
	KQU(16459605532062271798), KQU(  866316924816482864),
	KQU( 4593041209937286377), KQU( 8415491391910972138),
	KQU( 4171236715600528969), KQU(16637569303336782889),
	KQU( 2002011073439212680), KQU(17695124661097601411),
	KQU( 4627687053598611702), KQU( 7895831936020190403),
	KQU( 8455951300917267802), KQU( 2923861649108534854),
	KQU( 8344557563927786255), KQU( 6408671940373352556),
	KQU(12210227354536675772), KQU(14294804157294222295),
	KQU(10103022425071085127), KQU(10092959489504123771),
	KQU( 6554774405376736268), KQU(12629917718410641774),
	KQU( 6260933257596067126), KQU( 2460827021439369673),
	KQU( 2541962996717103668), KQU(  597377203127351475),
	KQU( 5316984203117315309), KQU( 4811211393563241961),
	KQU(13119698597255811641), KQU( 8048691512862388981),
	KQU(10216818971194073842), KQU( 4612229970165291764),
	KQU(10000980798419974770), KQU( 6877640812402540687),
	KQU( 1488727563290436992), KQU( 2227774069895697318),
	KQU(11237754507523316593), KQU(13478948605382290972),
	KQU( 1963583846976858124), KQU( 5512309205269276457),
	KQU( 3972770164717652347), KQU( 3841751276198975037),
	KQU(10283343042181903117), KQU( 8564001259792872199),
	KQU(16472187244722489221), KQU( 8953493499268945921),
	KQU( 3518747340357279580), KQU( 4003157546223963073),
	KQU( 3270305958289814590), KQU( 3966704458129482496),
	KQU( 8122141865926661939), KQU(14627734748099506653),
	KQU(13064426990862560568), KQU( 2414079187889870829),
	KQU( 5378461209354225306), KQU(10841985740128255566),
	KQU(  538582442885401738), KQU( 7535089183482905946),
	KQU(16117559957598879095), KQU( 8477890721414539741),
	KQU( 1459127491209533386), KQU(17035126360733620462),
	KQU( 8517668552872379126), KQU(10292151468337355014),
	KQU(17081267732745344157), KQU(13751455337946087178),
	KQU(14026945459523832966), KQU( 6653278775061723516),
	KQU(10619085543856390441), KQU( 2196343631481122885),
	KQU(10045966074702826136), KQU(10082317330452718282),
	KQU( 5920859259504831242), KQU( 9951879073426540617),
	KQU( 7074696649151414158), KQU(15808193543879464318),
	KQU( 7385247772746953374), KQU( 3192003544283864292),
	KQU(18153684490917593847), KQU(12423498260668568905),
	KQU(10957758099756378169), KQU(11488762179911016040),
	KQU( 2099931186465333782), KQU(11180979581250294432),
	KQU( 8098916250668367933), KQU( 3529200436790763465),
	KQU(12988418908674681745), KQU( 6147567275954808580),
	KQU( 3207503344604030989), KQU(10761592604898615360),
	KQU(  229854861031893504), KQU( 8809853962667144291),
	KQU(13957364469005693860), KQU( 7634287665224495886),
	KQU(12353487366976556874), KQU( 1134423796317152034),
	KQU( 2088992471334107068), KQU( 7393372127190799698),
	KQU( 1845367839871058391), KQU(  207922563987322884),
	KQU(11960870813159944976), KQU(12182120053317317363),
	KQU(17307358132571709283), KQU(13871081155552824936),
	KQU(18304446751741566262), KQU( 7178705220184302849),
	KQU(10929605677758824425), KQU(16446976977835806844),
	KQU(13723874412159769044), KQU( 6942854352100915216),
	KQU( 1726308474365729390), KQU( 2150078766445323155),
	KQU(15345558947919656626), KQU(12145453828874527201),
	KQU( 2054448620739726849), KQU( 2740102003352628137),
	KQU(11294462163577610655), KQU(  756164283387413743),
	KQU(17841144758438810880), KQU(10802406021185415861),
	KQU( 8716455530476737846), KQU( 6321788834517649606),
	KQU(14681322910577468426), KQU(17330043563884336387),
	KQU(12701802180050071614), KQU(14695105111079727151),
	KQU( 5112098511654172830), KQU( 4957505496794139973),
	KQU( 8270979451952045982), KQU(12307685939199120969),
	KQU(12425799408953443032), KQU( 8376410143634796588),
	KQU(16621778679680060464), KQU( 3580497854566660073),
	KQU( 1122515747803382416), KQU(  857664980960597599),
	KQU( 6343640119895925918), KQU(12878473260854462891),
	KQU(10036813920765722626), KQU(14451335468363173812),
	KQU( 5476809692401102807), KQU(16442255173514366342),
	KQU(13060203194757167104), KQU(14354124071243177715),
	KQU(15961249405696125227), KQU(13703893649690872584),
	KQU(  363907326340340064), KQU( 6247455540491754842),
	KQU(12242249332757832361), KQU(  156065475679796717),
	KQU( 9351116235749732355), KQU( 4590350628677701405),
	KQU( 1671195940982350389), KQU(13501398458898451905),
	KQU( 6526341991225002255), KQU( 1689782913778157592),
	KQU( 7439222350869010334), KQU(13975150263226478308),
	KQU(11411961169932682710), KQU(17204271834833847277),
	KQU(  541534742544435367), KQU( 6591191931218949684),
	KQU( 2645454775478232486), KQU( 4322857481256485321),
	KQU( 8477416487553065110), KQU(12902505428548435048),
	KQU(  971445777981341415), KQU(14995104682744976712),
	KQU( 4243341648807158063), KQU( 8695061252721927661),
	KQU( 5028202003270177222), KQU( 2289257340915567840),
	KQU(13870416345121866007), KQU(13994481698072092233),
	KQU( 6912785400753196481), KQU( 2278309315841980139),
	KQU( 4329765449648304839), KQU( 5963108095785485298),
	KQU( 4880024847478722478), KQU(16015608779890240947),
	KQU( 1866679034261393544), KQU(  914821179919731519),
	KQU( 9643404035648760131), KQU( 2418114953615593915),
	KQU(  944756836073702374), KQU(15186388048737296834),
	KQU( 7723355336128442206), KQU( 7500747479679599691),
	KQU(18013961306453293634), KQU( 2315274808095756456),
	KQU(13655308255424029566), KQU(17203800273561677098),
	KQU( 1382158694422087756), KQU( 5090390250309588976),
	KQU(  517170818384213989), KQU( 1612709252627729621),
	KQU( 1330118955572449606), KQU(  300922478056709885),
	KQU(18115693291289091987), KQU(13491407109725238321),
	KQU(15293714633593827320), KQU( 5151539373053314504),
	KQU( 5951523243743139207), KQU(14459112015249527975),
	KQU( 5456113959000700739), KQU( 3877918438464873016),
	KQU(12534071654260163555), KQU(15871678376893555041),
	KQU(11005484805712025549), KQU(16353066973143374252),
	KQU( 4358331472063256685), KQU( 8268349332210859288),
	KQU(12485161590939658075), KQU(13955993592854471343),
	KQU( 5911446886848367039), KQU(14925834086813706974),
	KQU( 6590362597857994805), KQU( 1280544923533661875),
	KQU( 1637756018947988164), KQU( 4734090064512686329),
	KQU(16693705263131485912), KQU( 6834882340494360958),
	KQU( 8120732176159658505), KQU( 2244371958905329346),
	KQU(10447499707729734021), KQU( 7318742361446942194),
	KQU( 8032857516355555296), KQU(14023605983059313116),
	KQU( 1032336061815461376), KQU( 9840995337876562612),
	KQU( 9869256223029203587), KQU(12227975697177267636),
	KQU(12728115115844186033), KQU( 7752058479783205470),
	KQU(  729733219713393087), KQU(12954017801239007622)
};
static const uint64_t init_by_array_64_expected[] = {
	KQU( 2100341266307895239), KQU( 8344256300489757943),
	KQU(15687933285484243894), KQU( 8268620370277076319),
	KQU(12371852309826545459), KQU( 8800491541730110238),
	KQU(18113268950100835773), KQU( 2886823658884438119),
	KQU( 3293667307248180724), KQU( 9307928143300172731),
	KQU( 7688082017574293629), KQU(  900986224735166665),
	KQU( 9977972710722265039), KQU( 6008205004994830552),
	KQU(  546909104521689292), KQU( 7428471521869107594),
	KQU(14777563419314721179), KQU(16116143076567350053),
	KQU( 5322685342003142329), KQU( 4200427048445863473),
	KQU( 4693092150132559146), KQU(13671425863759338582),
	KQU( 6747117460737639916), KQU( 4732666080236551150),
	KQU( 5912839950611941263), KQU( 3903717554504704909),
	KQU( 2615667650256786818), KQU(10844129913887006352),
	KQU(13786467861810997820), KQU(14267853002994021570),
	KQU(13767807302847237439), KQU(16407963253707224617),
	KQU( 4802498363698583497), KQU( 2523802839317209764),
	KQU( 3822579397797475589), KQU( 8950320572212130610),
	KQU( 3745623504978342534), KQU(16092609066068482806),
	KQU( 9817016950274642398), KQU(10591660660323829098),
	KQU(11751606650792815920), KQU( 5122873818577122211),
	KQU(17209553764913936624), KQU( 6249057709284380343),
	KQU(15088791264695071830), KQU(15344673071709851930),
	KQU( 4345751415293646084), KQU( 2542865750703067928),
	KQU(13520525127852368784), KQU(18294188662880997241),
	KQU( 3871781938044881523), KQU( 2873487268122812184),
	KQU(15099676759482679005), KQU(15442599127239350490),
	KQU( 6311893274367710888), KQU( 3286118760484672933),
	KQU( 4146067961333542189), KQU(13303942567897208770),
	KQU( 8196013722255630418), KQU( 4437815439340979989),
	KQU(15433791533450605135), KQU( 4254828956815687049),
	KQU( 1310903207708286015), KQU(10529182764462398549),
	KQU(14900231311660638810), KQU( 9727017277104609793),
	KQU( 1821308310948199033), KQU(11628861435066772084),
	KQU( 9469019138491546924), KQU( 3145812670532604988),
	KQU( 9938468915045491919), KQU( 1562447430672662142),
	KQU(13963995266697989134), KQU( 3356884357625028695),
	KQU( 4499850304584309747), KQU( 8456825817023658122),
	KQU(10859039922814285279), KQU( 8099512337972526555),
	KQU(  348006375109672149), KQU(11919893998241688603),
	KQU( 1104199577402948826), KQU(16689191854356060289),
	KQU(10992552041730168078), KQU( 7243733172705465836),
	KQU( 5668075606180319560), KQU(18182847037333286970),
	KQU( 4290215357664631322), KQU( 4061414220791828613),
	KQU(13006291061652989604), KQU( 7140491178917128798),
	KQU(12703446217663283481), KQU( 5500220597564558267),
	KQU(10330551509971296358), KQU(15958554768648714492),
	KQU( 5174555954515360045), KQU( 1731318837687577735),
	KQU( 3557700801048354857), KQU(13764012341928616198),
	KQU(13115166194379119043), KQU( 7989321021560255519),
	KQU( 2103584280905877040), KQU( 9230788662155228488),
	KQU(16396629323325547654), KQU(  657926409811318051),
	KQU(15046700264391400727), KQU( 5120132858771880830),
	KQU( 7934160097989028561), KQU( 6963121488531976245),
	KQU(17412329602621742089), KQU(15144843053931774092),
	KQU(17204176651763054532), KQU(13166595387554065870),
	KQU( 8590377810513960213), KQU( 5834365135373991938),
	KQU( 7640913007182226243), KQU( 3479394703859418425),
	KQU(16402784452644521040), KQU( 4993979809687083980),
	KQU(13254522168097688865), KQU(15643659095244365219),
	KQU( 5881437660538424982), KQU(11174892200618987379),
	KQU(  254409966159711077), KQU(17158413043140549909),
	KQU( 3638048789290376272), KQU( 1376816930299489190),
	KQU( 4622462095217761923), KQU(15086407973010263515),
	KQU(13253971772784692238), KQU( 5270549043541649236),
	KQU(11182714186805411604), KQU(12283846437495577140),
	KQU( 5297647149908953219), KQU(10047451738316836654),
	KQU( 4938228100367874746), KQU(12328523025304077923),
	KQU( 3601049438595312361), KQU( 9313624118352733770),
	KQU(13322966086117661798), KQU(16660005705644029394),
	KQU(11337677526988872373), KQU(13869299102574417795),
	KQU(15642043183045645437), KQU( 3021755569085880019),
	KQU( 4979741767761188161), KQU(13679979092079279587),
	KQU( 3344685842861071743), KQU(13947960059899588104),
	KQU(  305806934293368007), KQU( 5749173929201650029),
	KQU(11123724852118844098), KQU(15128987688788879802),
	KQU(15251651211024665009), KQU( 7689925933816577776),
	KQU(16732804392695859449), KQU(17087345401014078468),
	KQU(14315108589159048871), KQU( 4820700266619778917),
	KQU(16709637539357958441), KQU( 4936227875177351374),
	KQU( 2137907697912987247), KQU(11628565601408395420),
	KQU( 2333250549241556786), KQU( 5711200379577778637),
	KQU( 5170680131529031729), KQU(12620392043061335164),
	KQU(   95363390101096078), KQU( 5487981914081709462),
	KQU( 1763109823981838620), KQU( 3395861271473224396),
	KQU( 1300496844282213595), KQU( 6894316212820232902),
	KQU(10673859651135576674), KQU( 5911839658857903252),
	KQU(17407110743387299102), KQU( 8257427154623140385),
	KQU(11389003026741800267), KQU( 4070043211095013717),
	KQU(11663806997145259025), KQU(15265598950648798210),
	KQU(  630585789434030934), KQU( 3524446529213587334),
	KQU( 7186424168495184211), KQU(10806585451386379021),
	KQU(11120017753500499273), KQU( 1586837651387701301),
	KQU(17530454400954415544), KQU( 9991670045077880430),
	KQU( 7550997268990730180), KQU( 8640249196597379304),
	KQU( 3522203892786893823), KQU(10401116549878854788),
	KQU(13690285544733124852), KQU( 8295785675455774586),
	KQU(15535716172155117603), KQU( 3112108583723722511),
	KQU(17633179955339271113), KQU(18154208056063759375),
	KQU( 1866409236285815666), KQU(13326075895396412882),
	KQU( 8756261842948020025), KQU( 6281852999868439131),
	KQU(15087653361275292858), KQU(10333923911152949397),
	KQU( 5265567645757408500), KQU(12728041843210352184),
	KQU( 6347959327507828759), KQU(  154112802625564758),
	KQU(18235228308679780218), KQU( 3253805274673352418),
	KQU( 4849171610689031197), KQU(17948529398340432518),
	KQU(13803510475637409167), KQU(13506570190409883095),
	KQU(15870801273282960805), KQU( 8451286481299170773),
	KQU( 9562190620034457541), KQU( 8518905387449138364),
	KQU(12681306401363385655), KQU( 3788073690559762558),
	KQU( 5256820289573487769), KQU( 2752021372314875467),
	KQU( 6354035166862520716), KQU( 4328956378309739069),
	KQU(  449087441228269600), KQU( 5533508742653090868),
	KQU( 1260389420404746988), KQU(18175394473289055097),
	KQU( 1535467109660399420), KQU( 8818894282874061442),
	KQU(12140873243824811213), KQU(15031386653823014946),
	KQU( 1286028221456149232), KQU( 6329608889367858784),
	KQU( 9419654354945132725), KQU( 6094576547061672379),
	KQU(17706217251847450255), KQU( 1733495073065878126),
	KQU(16918923754607552663), KQU( 8881949849954945044),
	KQU(12938977706896313891), KQU(14043628638299793407),
	KQU(18393874581723718233), KQU( 6886318534846892044),
	KQU(14577870878038334081), KQU(13541558383439414119),
	KQU(13570472158807588273), KQU(18300760537910283361),
	KQU(  818368572800609205), KQU( 1417000585112573219),
	KQU(12337533143867683655), KQU(12433180994702314480),
	KQU(  778190005829189083), KQU(13667356216206524711),
	KQU( 9866149895295225230), KQU(11043240490417111999),
	KQU( 1123933826541378598), KQU( 6469631933605123610),
	KQU(14508554074431980040), KQU(13918931242962026714),
	KQU( 2870785929342348285), KQU(14786362626740736974),
	KQU(13176680060902695786), KQU( 9591778613541679456),
	KQU( 9097662885117436706), KQU(  749262234240924947),
	KQU( 1944844067793307093), KQU( 4339214904577487742),
	KQU( 8009584152961946551), KQU(16073159501225501777),
	KQU( 3335870590499306217), KQU(17088312653151202847),
	KQU( 3108893142681931848), KQU(16636841767202792021),
	KQU(10423316431118400637), KQU( 8008357368674443506),
	KQU(11340015231914677875), KQU(17687896501594936090),
	KQU(15173627921763199958), KQU(  542569482243721959),
	KQU(15071714982769812975), KQU( 4466624872151386956),
	KQU( 1901780715602332461), KQU( 9822227742154351098),
	KQU( 1479332892928648780), KQU( 6981611948382474400),
	KQU( 7620824924456077376), KQU(14095973329429406782),
	KQU( 7902744005696185404), KQU(15830577219375036920),
	KQU(10287076667317764416), KQU(12334872764071724025),
	KQU( 4419302088133544331), KQU(14455842851266090520),
	KQU(12488077416504654222), KQU( 7953892017701886766),
	KQU( 6331484925529519007), KQU( 4902145853785030022),
	KQU(17010159216096443073), KQU(11945354668653886087),
	KQU(15112022728645230829), KQU(17363484484522986742),
	KQU( 4423497825896692887), KQU( 8155489510809067471),
	KQU(  258966605622576285), KQU( 5462958075742020534),
	KQU( 6763710214913276228), KQU( 2368935183451109054),
	KQU(14209506165246453811), KQU( 2646257040978514881),
	KQU( 3776001911922207672), KQU( 1419304601390147631),
	KQU(14987366598022458284), KQU( 3977770701065815721),
	KQU(  730820417451838898), KQU( 3982991703612885327),
	KQU( 2803544519671388477), KQU(17067667221114424649),
	KQU( 2922555119737867166), KQU( 1989477584121460932),
	KQU(15020387605892337354), KQU( 9293277796427533547),
	KQU(10722181424063557247), KQU(16704542332047511651),
	KQU( 5008286236142089514), KQU(16174732308747382540),
	KQU(17597019485798338402), KQU(13081745199110622093),
	KQU( 8850305883842258115), KQU(12723629125624589005),
	KQU( 8140566453402805978), KQU(15356684607680935061),
	KQU(14222190387342648650), KQU(11134610460665975178),
	KQU( 1259799058620984266), KQU(13281656268025610041),
	KQU(  298262561068153992), KQU(12277871700239212922),
	KQU(13911297774719779438), KQU(16556727962761474934),
	KQU(17903010316654728010), KQU( 9682617699648434744),
	KQU(14757681836838592850), KQU( 1327242446558524473),
	KQU(11126645098780572792), KQU( 1883602329313221774),
	KQU( 2543897783922776873), KQU(15029168513767772842),
	KQU(12710270651039129878), KQU(16118202956069604504),
	KQU(15010759372168680524), KQU( 2296827082251923948),
	KQU(10793729742623518101), KQU(13829764151845413046),
	KQU(17769301223184451213), KQU( 3118268169210783372),
	KQU(17626204544105123127), KQU( 7416718488974352644),
	KQU(10450751996212925994), KQU( 9352529519128770586),
	KQU(  259347569641110140), KQU( 8048588892269692697),
	KQU( 1774414152306494058), KQU(10669548347214355622),
	KQU(13061992253816795081), KQU(18432677803063861659),
	KQU( 8879191055593984333), KQU(12433753195199268041),
	KQU(14919392415439730602), KQU( 6612848378595332963),
	KQU( 6320986812036143628), KQU(10465592420226092859),
	KQU( 4196009278962570808), KQU( 3747816564473572224),
	KQU(17941203486133732898), KQU( 2350310037040505198),
	KQU( 5811779859134370113), KQU(10492109599506195126),
	KQU( 7699650690179541274), KQU( 1954338494306022961),
	KQU(14095816969027231152), KQU( 5841346919964852061),
	KQU(14945969510148214735), KQU( 3680200305887550992),
	KQU( 6218047466131695792), KQU( 8242165745175775096),
	KQU(11021371934053307357), KQU( 1265099502753169797),
	KQU( 4644347436111321718), KQU( 3609296916782832859),
	KQU( 8109807992218521571), KQU(18387884215648662020),
	KQU(14656324896296392902), KQU(17386819091238216751),
	KQU(17788300878582317152), KQU( 7919446259742399591),
	KQU( 4466613134576358004), KQU(12928181023667938509),
	KQU(13147446154454932030), KQU(16552129038252734620),
	KQU( 8395299403738822450), KQU(11313817655275361164),
	KQU(  434258809499511718), KQU( 2074882104954788676),
	KQU( 7929892178759395518), KQU( 9006461629105745388),
	KQU( 5176475650000323086), KQU(11128357033468341069),
	KQU(12026158851559118955), KQU(14699716249471156500),
	KQU(  448982497120206757), KQU( 4156475356685519900),
	KQU( 6063816103417215727), KQU(10073289387954971479),
	KQU( 8174466846138590962), KQU( 2675777452363449006),
	KQU( 9090685420572474281), KQU( 6659652652765562060),
	KQU(12923120304018106621), KQU(11117480560334526775),
	KQU(  937910473424587511), KQU( 1838692113502346645),
	KQU(11133914074648726180), KQU( 7922600945143884053),
	KQU(13435287702700959550), KQU( 5287964921251123332),
	KQU(11354875374575318947), KQU(17955724760748238133),
	KQU(13728617396297106512), KQU( 4107449660118101255),
	KQU( 1210269794886589623), KQU(11408687205733456282),
	KQU( 4538354710392677887), KQU(13566803319341319267),
	KQU(17870798107734050771), KQU( 3354318982568089135),
	KQU( 9034450839405133651), KQU(13087431795753424314),
	KQU(  950333102820688239), KQU( 1968360654535604116),
	KQU(16840551645563314995), KQU( 8867501803892924995),
	KQU(11395388644490626845), KQU( 1529815836300732204),
	KQU(13330848522996608842), KQU( 1813432878817504265),
	KQU( 2336867432693429560), KQU(15192805445973385902),
	KQU( 2528593071076407877), KQU(  128459777936689248),
	KQU( 9976345382867214866), KQU( 6208885766767996043),
	KQU(14982349522273141706), KQU( 3099654362410737822),
	KQU(13776700761947297661), KQU( 8806185470684925550),
	KQU( 8151717890410585321), KQU(  640860591588072925),
	KQU(14592096303937307465), KQU( 9056472419613564846),
	KQU(14861544647742266352), KQU(12703771500398470216),
	KQU( 3142372800384138465), KQU( 6201105606917248196),
	KQU(18337516409359270184), KQU(15042268695665115339),
	KQU(15188246541383283846), KQU(12800028693090114519),
	KQU( 5992859621101493472), KQU(18278043971816803521),
	KQU( 9002773075219424560), KQU( 7325707116943598353),
	KQU( 7930571931248040822), KQU( 5645275869617023448),
	KQU( 7266107455295958487), KQU( 4363664528273524411),
	KQU(14313875763787479809), KQU(17059695613553486802),
	KQU( 9247761425889940932), KQU(13704726459237593128),
	KQU( 2701312427328909832), KQU(17235532008287243115),
	KQU(14093147761491729538), KQU( 6247352273768386516),
	KQU( 8268710048153268415), KQU( 7985295214477182083),
	KQU(15624495190888896807), KQU( 3772753430045262788),
	KQU( 9133991620474991698), KQU( 5665791943316256028),
	KQU( 7551996832462193473), KQU(13163729206798953877),
	KQU( 9263532074153846374), KQU( 1015460703698618353),
	KQU(17929874696989519390), KQU(18257884721466153847),
	KQU(16271867543011222991), KQU( 3905971519021791941),
	KQU(16814488397137052085), KQU( 1321197685504621613),
	KQU( 2870359191894002181), KQU(14317282970323395450),
	KQU(13663920845511074366), KQU( 2052463995796539594),
	KQU(14126345686431444337), KQU( 1727572121947022534),
	KQU(17793552254485594241), KQU( 6738857418849205750),
	KQU( 1282987123157442952), KQU(16655480021581159251),
	KQU( 6784587032080183866), KQU(14726758805359965162),
	KQU( 7577995933961987349), KQU(12539609320311114036),
	KQU(10789773033385439494), KQU( 8517001497411158227),
	KQU(10075543932136339710), KQU(14838152340938811081),
	KQU( 9560840631794044194), KQU(17445736541454117475),
	KQU(10633026464336393186), KQU(15705729708242246293),
	KQU( 1117517596891411098), KQU( 4305657943415886942),
	KQU( 4948856840533979263), KQU(16071681989041789593),
	KQU(13723031429272486527), KQU( 7639567622306509462),
	KQU(12670424537483090390), KQU( 9715223453097197134),
	KQU( 5457173389992686394), KQU(  289857129276135145),
	KQU(17048610270521972512), KQU(  692768013309835485),
	KQU(14823232360546632057), KQU(18218002361317895936),
	KQU( 3281724260212650204), KQU(16453957266549513795),
	KQU( 8592711109774511881), KQU(  929825123473369579),
	KQU(15966784769764367791), KQU( 9627344291450607588),
	KQU(10849555504977813287), KQU( 9234566913936339275),
	KQU( 6413807690366911210), KQU(10862389016184219267),
	KQU(13842504799335374048), KQU( 1531994113376881174),
	KQU( 2081314867544364459), KQU(16430628791616959932),
	KQU( 8314714038654394368), KQU( 9155473892098431813),
	KQU(12577843786670475704), KQU( 4399161106452401017),
	KQU( 1668083091682623186), KQU( 1741383777203714216),
	KQU( 2162597285417794374), KQU(15841980159165218736),
	KQU( 1971354603551467079), KQU( 1206714764913205968),
	KQU( 4790860439591272330), KQU(14699375615594055799),
	KQU( 8374423871657449988), KQU(10950685736472937738),
	KQU(  697344331343267176), KQU(10084998763118059810),
	KQU(12897369539795983124), KQU(12351260292144383605),
	KQU( 1268810970176811234), KQU( 7406287800414582768),
	KQU(  516169557043807831), KQU( 5077568278710520380),
	KQU( 3828791738309039304), KQU( 7721974069946943610),
	KQU( 3534670260981096460), KQU( 4865792189600584891),
	KQU(16892578493734337298), KQU( 9161499464278042590),
	KQU(11976149624067055931), KQU(13219479887277343990),
	KQU(14161556738111500680), KQU(14670715255011223056),
	KQU( 4671205678403576558), KQU(12633022931454259781),
	KQU(14821376219869187646), KQU(  751181776484317028),
	KQU( 2192211308839047070), KQU(11787306362361245189),
	KQU(10672375120744095707), KQU( 4601972328345244467),
	KQU(15457217788831125879), KQU( 8464345256775460809),
	KQU(10191938789487159478), KQU( 6184348739615197613),
	KQU(11425436778806882100), KQU( 2739227089124319793),
	KQU(  461464518456000551), KQU( 4689850170029177442),
	KQU( 6120307814374078625), KQU(11153579230681708671),
	KQU( 7891721473905347926), KQU(10281646937824872400),
	KQU( 3026099648191332248), KQU( 8666750296953273818),
	KQU(14978499698844363232), KQU(13303395102890132065),
	KQU( 8182358205292864080), KQU(10560547713972971291),
	KQU(11981635489418959093), KQU( 3134621354935288409),
	KQU(11580681977404383968), KQU(14205530317404088650),
	KQU( 5997789011854923157), KQU(13659151593432238041),
	KQU(11664332114338865086), KQU( 7490351383220929386),
	KQU( 7189290499881530378), KQU(15039262734271020220),
	KQU( 2057217285976980055), KQU(  555570804905355739),
	KQU(11235311968348555110), KQU(13824557146269603217),
	KQU(16906788840653099693), KQU( 7222878245455661677),
	KQU( 5245139444332423756), KQU( 4723748462805674292),
	KQU(12216509815698568612), KQU(17402362976648951187),
	KQU(17389614836810366768), KQU( 4880936484146667711),
	KQU( 9085007839292639880), KQU(13837353458498535449),
	KQU(11914419854360366677), KQU(16595890135313864103),
	KQU( 6313969847197627222), KQU(18296909792163910431),
	KQU(10041780113382084042), KQU( 2499478551172884794),
	KQU(11057894246241189489), KQU( 9742243032389068555),
	KQU(12838934582673196228), KQU(13437023235248490367),
	KQU(13372420669446163240), KQU( 6752564244716909224),
	KQU( 7157333073400313737), KQU(12230281516370654308),
	KQU( 1182884552219419117), KQU( 2955125381312499218),
	KQU(10308827097079443249), KQU( 1337648572986534958),
	KQU(16378788590020343939), KQU(  108619126514420935),
	KQU( 3990981009621629188), KQU( 5460953070230946410),
	KQU( 9703328329366531883), KQU(13166631489188077236),
	KQU( 1104768831213675170), KQU( 3447930458553877908),
	KQU( 8067172487769945676), KQU( 5445802098190775347),
	KQU( 3244840981648973873), KQU(17314668322981950060),
	KQU( 5006812527827763807), KQU(18158695070225526260),
	KQU( 2824536478852417853), KQU(13974775809127519886),
	KQU( 9814362769074067392), KQU(17276205156374862128),
	KQU(11361680725379306967), KQU( 3422581970382012542),
	KQU(11003189603753241266), KQU(11194292945277862261),
	KQU( 6839623313908521348), KQU(11935326462707324634),
	KQU( 1611456788685878444), KQU(13112620989475558907),
	KQU(  517659108904450427), KQU(13558114318574407624),
	KQU(15699089742731633077), KQU( 4988979278862685458),
	KQU( 8111373583056521297), KQU( 3891258746615399627),
	KQU( 8137298251469718086), KQU(12748663295624701649),
	KQU( 4389835683495292062), KQU( 5775217872128831729),
	KQU( 9462091896405534927), KQU( 8498124108820263989),
	KQU( 8059131278842839525), KQU(10503167994254090892),
	KQU(11613153541070396656), KQU(18069248738504647790),
	KQU(  570657419109768508), KQU( 3950574167771159665),
	KQU( 5514655599604313077), KQU( 2908460854428484165),
	KQU(10777722615935663114), KQU(12007363304839279486),
	KQU( 9800646187569484767), KQU( 8795423564889864287),
	KQU(14257396680131028419), KQU( 6405465117315096498),
	KQU( 7939411072208774878), KQU(17577572378528990006),
	KQU(14785873806715994850), KQU(16770572680854747390),
	KQU(18127549474419396481), KQU(11637013449455757750),
	KQU(14371851933996761086), KQU( 3601181063650110280),
	KQU( 4126442845019316144), KQU(10198287239244320669),
	KQU(18000169628555379659), KQU(18392482400739978269),
	KQU( 6219919037686919957), KQU( 3610085377719446052),
	KQU( 2513925039981776336), KQU(16679413537926716955),
	KQU(12903302131714909434), KQU( 5581145789762985009),
	KQU(12325955044293303233), KQU(17216111180742141204),
	KQU( 6321919595276545740), KQU( 3507521147216174501),
	KQU( 9659194593319481840), KQU(11473976005975358326),
	KQU(14742730101435987026), KQU(  492845897709954780),
	KQU(16976371186162599676), KQU(17712703422837648655),
	KQU( 9881254778587061697), KQU( 8413223156302299551),
	KQU( 1563841828254089168), KQU( 9996032758786671975),
	KQU(  138877700583772667), KQU(13003043368574995989),
	KQU( 4390573668650456587), KQU( 8610287390568126755),
	KQU(15126904974266642199), KQU( 6703637238986057662),
	KQU( 2873075592956810157), KQU( 6035080933946049418),
	KQU(13382846581202353014), KQU( 7303971031814642463),
	KQU(18418024405307444267), KQU( 5847096731675404647),
	KQU( 4035880699639842500), KQU(11525348625112218478),
	KQU( 3041162365459574102), KQU( 2604734487727986558),
	KQU(15526341771636983145), KQU(14556052310697370254),
	KQU(12997787077930808155), KQU( 9601806501755554499),
	KQU(11349677952521423389), KQU(14956777807644899350),
	KQU(16559736957742852721), KQU(12360828274778140726),
	KQU( 6685373272009662513), KQU(16932258748055324130),
	KQU(15918051131954158508), KQU( 1692312913140790144),
	KQU(  546653826801637367), KQU( 5341587076045986652),
	KQU(14975057236342585662), KQU(12374976357340622412),
	KQU(10328833995181940552), KQU(12831807101710443149),
	KQU(10548514914382545716), KQU( 2217806727199715993),
	KQU(12627067369242845138), KQU( 4598965364035438158),
	KQU(  150923352751318171), KQU(14274109544442257283),
	KQU( 4696661475093863031), KQU( 1505764114384654516),
	KQU(10699185831891495147), KQU( 2392353847713620519),
	KQU( 3652870166711788383), KQU( 8640653276221911108),
	KQU( 3894077592275889704), KQU( 4918592872135964845),
	KQU(16379121273281400789), KQU(12058465483591683656),
	KQU(11250106829302924945), KQU( 1147537556296983005),
	KQU( 6376342756004613268), KQU(14967128191709280506),
	KQU(18007449949790627628), KQU( 9497178279316537841),
	KQU( 7920174844809394893), KQU(10037752595255719907),
	KQU(15875342784985217697), KQU(15311615921712850696),
	KQU( 9552902652110992950), KQU(14054979450099721140),
	KQU( 5998709773566417349), KQU(18027910339276320187),
	KQU( 8223099053868585554), KQU( 7842270354824999767),
	KQU( 4896315688770080292), KQU(12969320296569787895),
	KQU( 2674321489185759961), KQU( 4053615936864718439),
	KQU(11349775270588617578), KQU( 4743019256284553975),
	KQU( 5602100217469723769), KQU(14398995691411527813),
	KQU( 7412170493796825470), KQU(  836262406131744846),
	KQU( 8231086633845153022), KQU( 5161377920438552287),
	KQU( 8828731196169924949), KQU(16211142246465502680),
	KQU( 3307990879253687818), KQU( 5193405406899782022),
	KQU( 8510842117467566693), KQU( 6070955181022405365),
	KQU(14482950231361409799), KQU(12585159371331138077),
	KQU( 3511537678933588148), KQU( 2041849474531116417),
	KQU(10944936685095345792), KQU(18303116923079107729),
	KQU( 2720566371239725320), KQU( 4958672473562397622),
	KQU( 3032326668253243412), KQU(13689418691726908338),
	KQU( 1895205511728843996), KQU( 8146303515271990527),
	KQU(16507343500056113480), KQU(  473996939105902919),
	KQU( 9897686885246881481), KQU(14606433762712790575),
	KQU( 6732796251605566368), KQU( 1399778120855368916),
	KQU(  935023885182833777), KQU(16066282816186753477),
	KQU( 7291270991820612055), KQU(17530230393129853844),
	KQU(10223493623477451366), KQU(15841725630495676683),
	KQU(17379567246435515824), KQU( 8588251429375561971),
	KQU(18339511210887206423), KQU(17349587430725976100),
	KQU(12244876521394838088), KQU( 6382187714147161259),
	KQU(12335807181848950831), KQU(16948885622305460665),
	KQU(13755097796371520506), KQU(14806740373324947801),
	KQU( 4828699633859287703), KQU( 8209879281452301604),
	KQU(12435716669553736437), KQU(13970976859588452131),
	KQU( 6233960842566773148), KQU(12507096267900505759),
	KQU( 1198713114381279421), KQU(14989862731124149015),
	KQU(15932189508707978949), KQU( 2526406641432708722),
	KQU(   29187427817271982), KQU( 1499802773054556353),
	KQU(10816638187021897173), KQU( 5436139270839738132),
	KQU( 6659882287036010082), KQU( 2154048955317173697),
	KQU(10887317019333757642), KQU(16281091802634424955),
	KQU(10754549879915384901), KQU(10760611745769249815),
	KQU( 2161505946972504002), KQU( 5243132808986265107),
	KQU(10129852179873415416), KQU(  710339480008649081),
	KQU( 7802129453068808528), KQU(17967213567178907213),
	KQU(15730859124668605599), KQU(13058356168962376502),
	KQU( 3701224985413645909), KQU(14464065869149109264),
	KQU( 9959272418844311646), KQU(10157426099515958752),
	KQU(14013736814538268528), KQU(17797456992065653951),
	KQU(17418878140257344806), KQU(15457429073540561521),
	KQU( 2184426881360949378), KQU( 2062193041154712416),
	KQU( 8553463347406931661), KQU( 4913057625202871854),
	KQU( 2668943682126618425), KQU(17064444737891172288),
	KQU( 4997115903913298637), KQU(12019402608892327416),
	KQU(17603584559765897352), KQU(11367529582073647975),
	KQU( 8211476043518436050), KQU( 8676849804070323674),
	KQU(18431829230394475730), KQU(10490177861361247904),
	KQU( 9508720602025651349), KQU( 7409627448555722700),
	KQU( 5804047018862729008), KQU(11943858176893142594),
	KQU(11908095418933847092), KQU( 5415449345715887652),
	KQU( 1554022699166156407), KQU( 9073322106406017161),
	KQU( 7080630967969047082), KQU(18049736940860732943),
	KQU(12748714242594196794), KQU( 1226992415735156741),
	KQU(17900981019609531193), KQU(11720739744008710999),
	KQU( 3006400683394775434), KQU(11347974011751996028),
	KQU( 3316999628257954608), KQU( 8384484563557639101),
	KQU(18117794685961729767), KQU( 1900145025596618194),
	KQU(17459527840632892676), KQU( 5634784101865710994),
	KQU( 7918619300292897158), KQU( 3146577625026301350),
	KQU( 9955212856499068767), KQU( 1873995843681746975),
	KQU( 1561487759967972194), KQU( 8322718804375878474),
	KQU(11300284215327028366), KQU( 4667391032508998982),
	KQU( 9820104494306625580), KQU(17922397968599970610),
	KQU( 1784690461886786712), KQU(14940365084341346821),
	KQU( 5348719575594186181), KQU(10720419084507855261),
	KQU(14210394354145143274), KQU( 2426468692164000131),
	KQU(16271062114607059202), KQU(14851904092357070247),
	KQU( 6524493015693121897), KQU( 9825473835127138531),
	KQU(14222500616268569578), KQU(15521484052007487468),
	KQU(14462579404124614699), KQU(11012375590820665520),
	KQU(11625327350536084927), KQU(14452017765243785417),
	KQU( 9989342263518766305), KQU( 3640105471101803790),
	KQU( 4749866455897513242), KQU(13963064946736312044),
	KQU(10007416591973223791), KQU(18314132234717431115),
	KQU( 3286596588617483450), KQU( 7726163455370818765),
	KQU( 7575454721115379328), KQU( 5308331576437663422),
	KQU(18288821894903530934), KQU( 8028405805410554106),
	KQU(15744019832103296628), KQU(  149765559630932100),
	KQU( 6137705557200071977), KQU(14513416315434803615),
	KQU(11665702820128984473), KQU(  218926670505601386),
	KQU( 6868675028717769519), KQU(15282016569441512302),
	KQU( 5707000497782960236), KQU( 6671120586555079567),
	KQU( 2194098052618985448), KQU(16849577895477330978),
	KQU(12957148471017466283), KQU( 1997805535404859393),
	KQU( 1180721060263860490), KQU(13206391310193756958),
	KQU(12980208674461861797), KQU( 3825967775058875366),
	KQU(17543433670782042631), KQU( 1518339070120322730),
	KQU(16344584340890991669), KQU( 2611327165318529819),
	KQU(11265022723283422529), KQU( 4001552800373196817),
	KQU(14509595890079346161), KQU( 3528717165416234562),
	KQU(18153222571501914072), KQU( 9387182977209744425),
	KQU(10064342315985580021), KQU(11373678413215253977),
	KQU( 2308457853228798099), KQU( 9729042942839545302),
	KQU( 7833785471140127746), KQU( 6351049900319844436),
	KQU(14454610627133496067), KQU(12533175683634819111),
	KQU(15570163926716513029), KQU(13356980519185762498)
};

TEST_BEGIN(test_gen_rand_32) {
	uint32_t array32[BLOCK_SIZE] JEMALLOC_ATTR(aligned(16));
	uint32_t array32_2[BLOCK_SIZE] JEMALLOC_ATTR(aligned(16));
	int i;
	uint32_t r32;
	sfmt_t *ctx;

	assert_d_le(get_min_array_size32(), BLOCK_SIZE,
	    "Array size too small");
	ctx = init_gen_rand(1234);
	fill_array32(ctx, array32, BLOCK_SIZE);
	fill_array32(ctx, array32_2, BLOCK_SIZE);
	fini_gen_rand(ctx);

	ctx = init_gen_rand(1234);
	for (i = 0; i < BLOCK_SIZE; i++) {
		if (i < COUNT_1) {
			assert_u32_eq(array32[i], init_gen_rand_32_expected[i],
			    "Output mismatch for i=%d", i);
		}
		r32 = gen_rand32(ctx);
		assert_u32_eq(r32, array32[i],
		    "Mismatch at array32[%d]=%x, gen=%x", i, array32[i], r32);
	}
	for (i = 0; i < COUNT_2; i++) {
		r32 = gen_rand32(ctx);
		assert_u32_eq(r32, array32_2[i],
		    "Mismatch at array32_2[%d]=%x, gen=%x", i, array32_2[i],
		    r32);
	}
	fini_gen_rand(ctx);
}
TEST_END

TEST_BEGIN(test_by_array_32) {
	uint32_t array32[BLOCK_SIZE] JEMALLOC_ATTR(aligned(16));
	uint32_t array32_2[BLOCK_SIZE] JEMALLOC_ATTR(aligned(16));
	int i;
	uint32_t ini[4] = {0x1234, 0x5678, 0x9abc, 0xdef0};
	uint32_t r32;
	sfmt_t *ctx;

	assert_d_le(get_min_array_size32(), BLOCK_SIZE,
	    "Array size too small");
	ctx = init_by_array(ini, 4);
	fill_array32(ctx, array32, BLOCK_SIZE);
	fill_array32(ctx, array32_2, BLOCK_SIZE);
	fini_gen_rand(ctx);

	ctx = init_by_array(ini, 4);
	for (i = 0; i < BLOCK_SIZE; i++) {
		if (i < COUNT_1) {
			assert_u32_eq(array32[i], init_by_array_32_expected[i],
			    "Output mismatch for i=%d", i);
		}
		r32 = gen_rand32(ctx);
		assert_u32_eq(r32, array32[i],
		    "Mismatch at array32[%d]=%x, gen=%x", i, array32[i], r32);
	}
	for (i = 0; i < COUNT_2; i++) {
		r32 = gen_rand32(ctx);
		assert_u32_eq(r32, array32_2[i],
		    "Mismatch at array32_2[%d]=%x, gen=%x", i, array32_2[i],
		    r32);
	}
	fini_gen_rand(ctx);
}
TEST_END

TEST_BEGIN(test_gen_rand_64) {
	uint64_t array64[BLOCK_SIZE64] JEMALLOC_ATTR(aligned(16));
	uint64_t array64_2[BLOCK_SIZE64] JEMALLOC_ATTR(aligned(16));
	int i;
	uint64_t r;
	sfmt_t *ctx;

	assert_d_le(get_min_array_size64(), BLOCK_SIZE64,
	    "Array size too small");
	ctx = init_gen_rand(4321);
	fill_array64(ctx, array64, BLOCK_SIZE64);
	fill_array64(ctx, array64_2, BLOCK_SIZE64);
	fini_gen_rand(ctx);

	ctx = init_gen_rand(4321);
	for (i = 0; i < BLOCK_SIZE64; i++) {
		if (i < COUNT_1) {
			assert_u64_eq(array64[i], init_gen_rand_64_expected[i],
			    "Output mismatch for i=%d", i);
		}
		r = gen_rand64(ctx);
		assert_u64_eq(r, array64[i],
		    "Mismatch at array64[%d]=%"FMTx64", gen=%"FMTx64, i,
		    array64[i], r);
	}
	for (i = 0; i < COUNT_2; i++) {
		r = gen_rand64(ctx);
		assert_u64_eq(r, array64_2[i],
		    "Mismatch at array64_2[%d]=%"FMTx64" gen=%"FMTx64"", i,
		    array64_2[i], r);
	}
	fini_gen_rand(ctx);
}
TEST_END

TEST_BEGIN(test_by_array_64) {
	uint64_t array64[BLOCK_SIZE64] JEMALLOC_ATTR(aligned(16));
	uint64_t array64_2[BLOCK_SIZE64] JEMALLOC_ATTR(aligned(16));
	int i;
	uint64_t r;
	uint32_t ini[] = {5, 4, 3, 2, 1};
	sfmt_t *ctx;

	assert_d_le(get_min_array_size64(), BLOCK_SIZE64,
	    "Array size too small");
	ctx = init_by_array(ini, 5);
	fill_array64(ctx, array64, BLOCK_SIZE64);
	fill_array64(ctx, array64_2, BLOCK_SIZE64);
	fini_gen_rand(ctx);

	ctx = init_by_array(ini, 5);
	for (i = 0; i < BLOCK_SIZE64; i++) {
		if (i < COUNT_1) {
			assert_u64_eq(array64[i], init_by_array_64_expected[i],
			    "Output mismatch for i=%d", i);
		}
		r = gen_rand64(ctx);
		assert_u64_eq(r, array64[i],
		    "Mismatch at array64[%d]=%"FMTx64" gen=%"FMTx64, i,
		    array64[i], r);
	}
	for (i = 0; i < COUNT_2; i++) {
		r = gen_rand64(ctx);
		assert_u64_eq(r, array64_2[i],
		    "Mismatch at array64_2[%d]=%"FMTx64" gen=%"FMTx64, i,
		    array64_2[i], r);
	}
	fini_gen_rand(ctx);
}
TEST_END

int
main(void) {
	return test(
	    test_gen_rand_32,
	    test_by_array_32,
	    test_gen_rand_64,
	    test_by_array_64);
}
