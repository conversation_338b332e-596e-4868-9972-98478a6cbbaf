This directory contains some useful files and code.
Unlike the code in ../src, everything here is in the public domain.

If any of the makes fail, you're probably not using the same libraries
used to build Lua. Set MYLIBS in Makefile accordingly.

all.c
	Full Lua interpreter in a single file.
	Do "make one" for a demo.

lua.hpp
	Lua header files for C++ using 'extern "C"'.

lua.ico
	A Lua icon for Windows (and web sites: save as favicon.ico).
	Drawn by hand by <PERSON> <<EMAIL>>.

lua.pc
	pkg-config data for Lua

luavs.bat
	Script to build Lua under "Visual Studio .NET Command Prompt".
	Run it from the toplevel as etc\luavs.bat.

min.c
	A minimal Lua interpreter.
	Good for learning and for starting your own.
	Do "make min" for a demo.

noparser.c
	Linking with noparser.o avoids loading the parsing modules in lualib.a.
	Do "make noparser" for a demo.

strict.lua
	Traps uses of undeclared global variables.
	Do "make strict" for a demo.

