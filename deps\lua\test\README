These are simple tests for Lu<PERSON>.  Some of them contain useful code.
They are meant to be run to make sure <PERSON><PERSON> is built correctly and also
to be read, to see how Lua programs look.

Here is a one-line summary of each program:

   bisect.lua		bisection method for solving non-linear equations
   cf.lua		temperature conversion table (celsius to farenheit)
   echo.lua             echo command line arguments
   env.lua              environment variables as automatic global variables
   factorial.lua	factorial without recursion
   fib.lua		fibonacci function with cache
   fibfor.lua		fibonacci numbers with coroutines and generators
   globals.lua		report global variable usage
   hello.lua		the first program in every language
   life.lua		Conway's Game of Life
   luac.lua	 	bare-bones luac
   printf.lua		an implementation of printf
   readonly.lua		make global variables readonly
   sieve.lua		the sieve of of Eratosthenes programmed with coroutines
   sort.lua		two implementations of a sort function
   table.lua		make table, grouping all data for the same item
   trace-calls.lua	trace calls
   trace-globals.lua	trace assigments to global variables
   xd.lua		hex dump

