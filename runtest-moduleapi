#!/bin/sh
TCL_VERSIONS="8.5 8.6"
TCLSH=""

for VERSION in $TCL_VERSIONS; do
	TCL=`which tclsh$VERSION 2>/dev/null` && TCLSH=$TCL
done

if [ -z $TCLSH ]
then
    echo "You need tcl 8.5 or newer in order to run the Redis test"
    exit 1
fi

make -C tests/modules && \
$TCLSH tests/test_helper.tcl \
--single unit/moduleapi/commandfilter \
--single unit/moduleapi/fork \
--single unit/moduleapi/testrdb \
--single unit/moduleapi/infotest \
--single unit/moduleapi/propagate \
--single unit/moduleapi/hooks \
--single unit/moduleapi/misc \
--single unit/moduleapi/blockonkeys \
--single unit/moduleapi/scan \
--single unit/moduleapi/datatype \
--single unit/moduleapi/auth \
"${@}"
